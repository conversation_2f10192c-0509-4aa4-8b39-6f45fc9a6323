import re
import logging
import operator
import json
import polars as pl

from typing import Optional, Union, List, Dict, Any
from pydantic import BaseModel, validator, Field, ValidationError
from enum import Enum, auto
from functools import reduce

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("quantity_validator")

class SeverityLevel(Enum):
    LOW = 1      # Minor issue, probably correct
    MEDIUM = 2   # Needs review, could be correct
    HIGH = 3     # Likely incorrect
    CRITICAL = 4 # Almost certainly incorrect
    INVALID = 5  # Invalid format/value

class IssueType(Enum):
    DOUBLE_FEET = auto()         # 1' 1'
    DOUBLE_INCHES = auto()       # 1" 1"
    WRONG_ORDER = auto()         # 1" 1'
    MISSING_UNITS = auto()       # 1 1
    SMALL_FEET_VALUE = auto()    # Small values like 1' that should likely be inches
    SMALL_INCH_VALUE = auto()    # Small values like 1" that are valid but might need review
    LARGE_INCH_VALUE = auto()    # Values like 13" that should likely be feet
    DECIMAL_INCH = auto()        # 1.1" which is uncommon
    MIXED_FORMAT = auto()        # When the format is inconsistent
    INVALID_FORMAT = auto()      # When the format doesn't match expected patterns

class ValidationIssue(BaseModel):
    issue_type: IssueType
    severity: SeverityLevel
    message: str
    original_value: str

class QuantityValidationResult(BaseModel):
    original_value: str
    is_valid: bool
    is_pipe_item: bool
    issues: List[ValidationIssue] = []
    normalized_value: Optional[float] = None

class LinearMeasurement(BaseModel):
    """Model for validating linear measurements (feet and inches)"""
    original: str
    feet: Optional[float] = 0
    inches: Optional[float] = 0

    @validator('original')
    def validate_format(cls, v):
        if not isinstance(v, str):
            try:
                # Try to convert to string if it's a number
                return str(v)
            except:
                raise ValueError(f"Cannot convert {v} to string")
        return v

    def total_inches(self) -> float:
        """Convert to total inches"""
        return (self.feet * 12) + self.inches

    def total_feet(self) -> float:
        """Convert to total feet"""
        return self.feet + (self.inches / 12)

def detect_pipe_items(df: pl.DataFrame) -> pl.DataFrame:
    """
    Determine if an item is a pipe based on its description.

    Args:
        df: DataFrame with 'material_description' column

    Returns:
        df: DataFrame with 'is_pipe_item' column added
    """
    # List of exclusion words
    exclusion_words = [
        'bend', 'support', 'supports', 'shoe', 'guide', 'nipple', 'base',
        'pipet', 'stanchin', 'stanchion', 'pipe rack', 'clamp', 'anvil',
        'pipe sits', 'repad', 'dummy leg', 'd-leg'
    ]

    # Ensure material_col is null-safe
    material_col = pl.col("material_description").fill_null("")

    # Build OR-combined exclusion expression
    exclusions_expr = reduce(
        operator.or_,
        [material_col.str.contains(fr"(?i){word}") for word in exclusion_words]
    )

    # Main pipe match expression (case-insensitive)
    pipe_expr = (
        material_col.str.contains(r"(?i)\bpipe\b") |
        material_col.str.contains(r"(?i)pipe,")
    )

    is_pipe_expr = pipe_expr & ~exclusions_expr

    # Apply to DataFrame
    result_df = df.with_columns(
        is_pipe_expr.alias("_audit_is_pipe_item")
    )

    return result_df

def parse_linear_measurement(value: str) -> Optional[LinearMeasurement]:
    """
    Parse a string containing a linear measurement into feet and inches.

    Args:
        value: The string to parse

    Returns:
        Optional[LinearMeasurement]: Parsed measurement or None if invalid
    """
    if not isinstance(value, str):
        try:
            # Try to convert numbers to strings
            value = str(value)
        except:
            logger.error(f"Cannot convert {value} to string")
            return None

    # Clean the input value
    value = value.strip()

    # Define patterns for different formats

    # Pattern for "x'y\"" format (e.g., 1'2")
    pattern1 = r"(\d+(?:\.\d+)?)'[ -]*(\d+(?:\.\d+)?)(?:[ -]*(\d+)/(\d+))?\"?"

    # Pattern for feet only (e.g., 1')
    pattern2 = r"(\d+(?:\.\d+)?)'$"

    # Pattern for inches only (e.g., 1")
    pattern3 = r"(\d+(?:\.\d+)?)(?:[ -]*(\d+)/(\d+))?\""

    # Pattern for mixed fraction feet (e.g., 1-1/2')
    pattern4 = r"(\d+)[ -]+(\d+)/(\d+)'"

    # Pattern for mixed fraction inches (e.g., 1-1/2")
    pattern5 = r"(\d+)[ -]+(\d+)/(\d+)\""

    # Try to match the patterns
    # First pattern: x'y" (feet and inches)
    match = re.match(pattern1, value)
    if match:
        feet = float(match.group(1))
        inches = float(match.group(2))

        # Check for fractions
        if match.group(3) and match.group(4):
            fraction = float(match.group(3)) / float(match.group(4))
            inches += fraction

        return LinearMeasurement(original=value, feet=feet, inches=inches)

    # Second pattern: x' (feet only)
    match = re.match(pattern2, value)
    if match:
        feet = float(match.group(1))
        return LinearMeasurement(original=value, feet=feet, inches=0)

    # Third pattern: x" (inches only)
    match = re.match(pattern3, value)
    if match:
        inches = float(match.group(1))

        # Check for fractions
        if match.group(2) and match.group(3):
            fraction = float(match.group(2)) / float(match.group(3))
            inches += fraction

        return LinearMeasurement(original=value, feet=0, inches=inches)

    # Fourth pattern: mixed fraction feet (e.g., 1-1/2')
    match = re.match(pattern4, value)
    if match:
        whole = float(match.group(1))
        numerator = float(match.group(2))
        denominator = float(match.group(3))
        feet = whole + (numerator / denominator)
        return LinearMeasurement(original=value, feet=feet, inches=0)

    # Fifth pattern: mixed fraction inches (e.g., 1-1/2")
    match = re.match(pattern5, value)
    if match:
        whole = float(match.group(1))
        numerator = float(match.group(2))
        denominator = float(match.group(3))
        inches = whole + (numerator / denominator)
        return LinearMeasurement(original=value, feet=0, inches=inches)

    # Handle decimal numbers (assume feet)
    if re.match(r"^\d+(?:\.\d+)?$", value):
        try:
            feet = float(value)

            # For pipe items, we should create the measurement but not validate it yet
            # The validation for missing units will happen in validate_quantity
            return LinearMeasurement(original=value, feet=feet, inches=0)
        except ValueError:
            logger.error(f"Failed to convert {value} to float")
            return None

    # If no pattern matches
    logger.warning(f"No pattern matches for value: {value}")
    return None

# Append a new issue to an existing JSON string
def append_issue(existing_json, issue_type, message, severity):
    if existing_json is None:
        issues = []
    else:
        try:
            issues = json.loads(existing_json)
        except json.JSONDecodeError:
            issues = []
    issues.append({"issue_type": issue_type, "message": message, "severity": severity})
    return json.dumps(issues)

def validate_quantity(df: pl.DataFrame) -> pl.DataFrame:
    """
    Validate a quantity value based on whether it's for a pipe item or not.

    Args:
        df: DataFrame with quantity column and is_pipe_item column

    Returns:
        df: DataFrame with validation results
    """

    df = df.with_columns(pl.lit(None).alias("_audit_validation_issues"))
    df = df.with_columns(pl.lit(0).alias("_audit_severity"))

    # Set of values to ignore (treat as valid)
    ignore_values = {"deleted", "not used", "n/a", "na", "none"}

    # Null-safe reference
    quantity_col = pl.col("quantity").cast(pl.String).str.strip_chars()
    quantity_f = quantity_col.cast(pl.Float64, strict=False)

    # Vectorized check for None or NaN
    is_ignore_value = (quantity_col.str.to_lowercase().is_in(ignore_values))
    is_invalid = (quantity_col.is_null() | quantity_col.eq("")) & ~is_ignore_value

    is_pipe_expr = pl.col("_audit_is_pipe_item")

    # Detects blank or null values, and initializes is_valid flag if they are not ignored
    df = df.with_columns([
        ~is_invalid.alias("_audit_is_valid"),
        pl.when(is_invalid).then(
            pl.struct(["_audit_validation_issues", pl.lit("Blank or null quantity value").alias("message")])
            .map_elements(lambda row: append_issue(row["_audit_validation_issues"],
                        IssueType.INVALID_FORMAT.value, row["message"], SeverityLevel.MEDIUM.value),
                        return_dtype=pl.String)
        ).otherwise(pl.col("_audit_validation_issues")).alias("_audit_validation_issues"),
        pl.when(is_invalid & (SeverityLevel.MEDIUM.value > pl.col("_audit_severity")))
          .then(SeverityLevel.MEDIUM.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity")
    ])

    # For ignore terms, remove the severity and set the normalized value to 0
    df = df.with_columns([
        pl.when(is_ignore_value).then(0).otherwise(None).alias("_audit_normalized_value"),
        pl.when(is_ignore_value).then(0).otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # NON-PIPE quantity checks
    # Check validity of quantity values for non-pipe items
    is_int_like = quantity_f.is_not_null() & (quantity_f % 1 == 0)
    can_parse_float = ~quantity_f.is_null()
    not_pipes = ~is_pipe_expr

    # Set normalized value for non-pipe items
    normalized_value_expr = pl.when(not_pipes & can_parse_float).then(quantity_f).otherwise(pl.col("_audit_normalized_value")).alias("_audit_normalized_value")
    df = df.with_columns([normalized_value_expr])

    # Check for pipe items with missing units
    pipe_missing_units_expr = pl.when(is_pipe_expr & can_parse_float & ~is_invalid).then(pl.concat_str([
              pl.lit("Missing units (' or \") for pipe measurement: "),
              pl.col("quantity").cast(pl.Utf8)
          ])).otherwise(None)


    df = df.with_columns([
        pl.when(pipe_missing_units_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", pipe_missing_units_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"],
                            IssueType.MISSING_UNITS.value, row["message"], SeverityLevel.CRITICAL.value),
                            return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(pipe_missing_units_expr.is_not_null()).then(SeverityLevel.CRITICAL.value).otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
        pl.when(pipe_missing_units_expr.is_not_null())
            .then(quantity_f)
            .otherwise(pl.col("_audit_normalized_value"))
            .alias("_audit_normalized_value")
    ])

    # Check non-pipe item quantity values
    # HIGH severity when non-pipe item quantity which is numeric but not an int
    # HIGH severity when non-pipe item quantity cannot be parsed
    non_pipe_not_int_expr = (pl.when(not_pipes & ~is_int_like & can_parse_float & ~is_ignore_value)
                               .then(pl.lit("Non-pipe item quantity should be an integer"))
                               .when(not_pipes & ~can_parse_float & ~is_ignore_value)
                               .then(pl.lit("Unable to parse quantity as a number"))
                               .otherwise(None))

    df = df.with_columns([
        pl.when(non_pipe_not_int_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", non_pipe_not_int_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.INVALID_FORMAT.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(non_pipe_not_int_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value)
          .otherwise(pl.col("_audit_severity"))
          .alias("_audit_severity"),
        pl.when(non_pipe_not_int_expr.is_not_null())
          .then(quantity_f)
          .otherwise(pl.col("_audit_normalized_value"))
          .alias("_audit_normalized_value")
    ])

    # More pipe item quantity validation
    pipe_item_checks = is_pipe_expr & (pl.col("_audit_severity") == 0)

    multiple_feet_expr = (pl.when(pipe_item_checks & (quantity_col.str.extract_all(r"'").list.len() > 1))
                            .then(pl.concat_str([
                                    pl.lit("Multiple feet symbols found in pipe measurement: "),
                                    pl.col("quantity")
                                ]))
                            .otherwise(None))
    df = df.with_columns([
        pl.when(multiple_feet_expr.is_not_null())
            .then(
                    pl.struct(["_audit_validation_issues", multiple_feet_expr.alias("message")])
                    .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.INVALID_FORMAT.value,
                                row["message"], SeverityLevel.HIGH.value), return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues")).alias("_audit_validation_issues"),
        pl.when(multiple_feet_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value).otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    multiple_inch_expr = (pl.when(pipe_item_checks & (quantity_col.str.extract_all(r'"').list.len() > 1))
                            .then(pl.concat_str([
                                    pl.lit("Multiple inches symbols found in pipe measurement: "),
                                    pl.col("quantity")
                                ]))
                            .otherwise(None))

    df = df.with_columns([
        pl.when(multiple_inch_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", multiple_inch_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.INVALID_FORMAT.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(multiple_inch_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Check for wrong order (inches before feet)
    inches_before_feet_expr = pl.when(pipe_item_checks & pl.col("quantity").str.contains(r'"[^\'"]*\'')).then(pl.concat_str([
            pl.lit("Inches symbol before feet symbol: "),
            pl.col("quantity")
        ])).otherwise(None)
    df = df.with_columns([
        pl.when(inches_before_feet_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", inches_before_feet_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.WRONG_ORDER.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(inches_before_feet_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value).otherwise(pl.col("_audit_severity"))
          .alias("_audit_severity"),
    ])

    # Original regex
    # inches_expr = quantity_col.str.contains(r"(\d+(?:\.\d+)?)'[ -]*(\d+(?:\.\d+)?)$", literal=False).alias("inches")
    # feet_expr = quantity_col.str.contains(r"(\d+(?:\.\d+)?)'?[ -]+(\d+(?:\.\d+)?)$", literal=False).alias("feet")

    # Check for partial match - missing inches symbol
    missing_feet_expr = pl.when(pipe_item_checks & (
        quantity_col.str.contains(r"\d+\s*\d\"") &             # Looks like feet + inches
        ~quantity_col.str.contains(r"'")                      # But missing inch symbol
    )).then(pl.concat_str([
            pl.lit("Missing feet symbol: "),
            quantity_col
        ])).otherwise(None)

    df = df.with_columns([
        pl.when(missing_feet_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", missing_feet_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.MISSING_UNITS.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(missing_feet_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Check for partial match - missing feet symbol
    missing_inches_expr = pl.when(pipe_item_checks & (
        quantity_col.str.contains(r"\d+'\s*\d") &             # Looks like feet + inches
        ~quantity_col.str.contains(r'"')                      # But missing inch symbol
    )).then(pl.concat_str([
            pl.lit("Missing inch symbol: "),
            quantity_col
        ])).otherwise(None)

    df = df.with_columns([
        pl.when(missing_inches_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", missing_inches_expr.alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.MISSING_UNITS.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(missing_inches_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Parse linear mesaurements

    # Store LinearMeasurement objects
    df = df.with_columns(
        pl.when(pipe_item_checks)
          .then(quantity_col.map_elements(parse_linear_measurement, return_dtype=pl.Object))
          .otherwise(None)
        .alias("_tmp_linear_measurement")
    )

    # Check for invalid measurements
    df = df.with_columns([
        pl.when(pipe_item_checks & pl.col("_tmp_linear_measurement").is_null())
            .then(
                pl.struct(["_audit_validation_issues", pl.concat_str([pl.lit("Quantity: Cannot parse measurement: "), quantity_col]).alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.INVALID_FORMAT.value, row["message"], SeverityLevel.CRITICAL.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(pipe_item_checks & pl.col("_tmp_linear_measurement").is_null() & (SeverityLevel.CRITICAL.value > pl.col("_audit_severity")))
          .then(SeverityLevel.CRITICAL.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Small feet
    small_feet_expr = (pl.col("_tmp_linear_measurement")
           .map_elements(lambda m: (True if m is not None and (m.feet < 12 and m.inches == 0) else None), return_dtype=pl.Boolean))

    df = df.with_columns([
        pl.when(small_feet_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", pl.concat_str([pl.lit("Small feet value (possibly should be inches): "), pl.col("quantity")]).alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.SMALL_FEET_VALUE.value, row["message"], SeverityLevel.HIGH.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(small_feet_expr.is_not_null() & (SeverityLevel.HIGH.value > pl.col("_audit_severity")))
          .then(SeverityLevel.HIGH.value)
          .otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Small inch values (need review)
    small_inch_expr = (pl.col("_tmp_linear_measurement")
           .map_elements(lambda m: (True if m is not None and (m.feet == 0 and m.inches < 12) else None), return_dtype=pl.Boolean))

    df = df.with_columns([
        pl.when(small_inch_expr.is_not_null())
            .then(
                pl.struct(["_audit_validation_issues", pl.concat_str([pl.lit("Small inch value (< 12): "), pl.col("quantity")]).alias("message")])
                .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.SMALL_INCH_VALUE.value, row["message"], SeverityLevel.MEDIUM.value),
                              return_dtype=pl.String)
            )
            .otherwise(pl.col("_audit_validation_issues"))
            .alias("_audit_validation_issues"),
        pl.when(small_inch_expr.is_not_null() & (SeverityLevel.MEDIUM.value > pl.col("_audit_severity")))
          .then(SeverityLevel.MEDIUM.value)
          .otherwise(pl.col("_audit_severity"))
          .alias("_audit_severity"),
    ])

    # # Decimal inch values (uncommon)
    decimal_inch_expr = (pl.col("_tmp_linear_measurement")
           .map_elements(lambda m: (True if m is not None and (m.inches > 0 and m.inches != int(m.inches)) else None), return_dtype=pl.Boolean))

    df = df.with_columns([
        pl.when(decimal_inch_expr.is_not_null())
          .then(
              pl.struct(["_audit_validation_issues", pl.concat_str([pl.lit("Decimal inch value: "), pl.col("quantity")]).alias("message")])
              .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.DECIMAL_INCH.value, row["message"], SeverityLevel.MEDIUM.value),
                            return_dtype=pl.String)
          )
          .otherwise(pl.col("_audit_validation_issues"))
          .alias("_audit_validation_issues"),
        pl.when(decimal_inch_expr.is_not_null() & (SeverityLevel.MEDIUM.value > pl.col("_audit_severity"))).then(SeverityLevel.MEDIUM.value).otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Large inch inch values (might be feet)
    large_inch_expr = (pl.col("_tmp_linear_measurement")
           .map_elements(lambda m: (True if m is not None and (m.feet == 0 and m.inches > 12) else None), return_dtype=pl.Boolean))

    df = df.with_columns([
        pl.when(large_inch_expr.is_not_null())
          .then(
              pl.struct(["_audit_validation_issues", pl.concat_str([pl.lit("Large inch value: "), pl.col("quantity")]).alias("message")])
              .map_elements(lambda row: append_issue(row["_audit_validation_issues"], IssueType.LARGE_INCH_VALUE.value, row["message"], SeverityLevel.MEDIUM.value),
                            return_dtype=pl.String)
          )
          .otherwise(pl.col("_audit_validation_issues"))
          .alias("_audit_validation_issues"),
        pl.when(large_inch_expr.is_not_null() & (SeverityLevel.MEDIUM.value > pl.col("_audit_severity"))).then(SeverityLevel.MEDIUM.value).otherwise(pl.col("_audit_severity")).alias("_audit_severity"),
    ])

    # Update normalized values for pipe items
    normalize_pipe_value_expr = (pl.col("_tmp_linear_measurement")
                                .map_elements(lambda m: (m.total_feet() if m is not None else None), return_dtype=pl.Float64))

    # Store normalized value for pipe items (convert to total feet)
    df = df.with_columns([
        pl.when(normalize_pipe_value_expr.is_not_null()).then(normalize_pipe_value_expr).otherwise(pl.col("_audit_normalized_value")).alias("_audit_normalized_value")
    ])

    df = df.with_columns([
        # pl.when(pl.col("_audit_severity") > 0).then(
        #     quantity_col
        # ).otherwise(pl.lit("")).alias("original_value"),
        pl.when(pl.col("_audit_severity") > 0).then(False).otherwise(pl.col("_audit_is_valid")).alias("_audit_is_valid")
    ])

    df = df.drop("_tmp_linear_measurement")

    with pl.Config(tbl_cols=-1, tbl_rows=100, fmt_str_lengths=100):
        print(df)

    return df


def validate_dataframe(df: pl.DataFrame) -> pl.DataFrame:
    """
    Validate quantity values in a dataframe based on material description.

    Args:
        df: DataFrame with 'material_description' and 'quantity' columns

    Returns:
        pl.DataFrame: Original dataframe with validation columns added
    """
    # Ensure required columns exist
    required_cols = ['material_description', 'quantity']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"DataFrame is missing required column: {col}")

    # Create new columns for validation results
    result_df = df.clone()
    result_df = detect_pipe_items(result_df)

    # Apply validation to each row
    validation_results = []

    result_df = validate_quantity(result_df)
    return result_df

# Example usage
if __name__ == "__main__":
    # Create a sample dataframe for testing

    data = [
        {"material_description": "PIPE - VALID #1", "quantity": "36'"},
        {"material_description": "PIPE - VALID #2", "quantity": "36' - 12\""},
        {"material_description": "PIPE - with missing units", "quantity": "36"},
        {"material_description": "PIPE - with integer quantity", "quantity": "300"},
        {"material_description": "PIPE - multiple feet symbols", "quantity": "10' -13'"},
        {"material_description": "PIPE - multiple inches symbols", "quantity": "13\" 22\""},
        {"material_description": "PIPE - partial missing feet", "quantity": "10 13\""},
        {"material_description": "PIPE - partial missing inch", "quantity": "10' 13"},
        {"material_description": "PIPE - Inches before feet", "quantity": "1\" 1'"},
        {"material_description": "PIPE - Cannot parse measurement", "quantity": "1' x1x\""},
        {"material_description": "PIPE - Small feet value", "quantity": "1'"},
        {"material_description": "PIPE - Small inch value", "quantity": "3\""},
        {"material_description": "PIPE - Large inch value", "quantity": "30\""},
        {"material_description": "PIPE - Large inch value #2", "quantity": "0' 30\""},
        {"material_description": "PIPE - Decimal inch value", "quantity": "1.2\""},
        {"material_description": "NONPIPE - VALID #1", "quantity": "2"},
        {"material_description": "NONPIPE - ignore null quantity - NONE", "quantity": "none"},
        {"material_description": "NONPIPE - ignore null quantity - NA", "quantity": "na"},
        {"material_description": "NONPIPE - ignore null quantity - N/A", "quantity": "n/a"},
        {"material_description": "NONPIPE - Float number", "quantity": "2.5"},
        {"material_description": "NONPIPE - invalid item quantity", "quantity": ""},
        {"material_description": "NONPIPE - double feet", "quantity": "10' 12\""},
        {"material_description": "NONPIPE - double inches", "quantity": "12\" 10'"},
        {"material_description": "NONPIPE - Inches before feet", "quantity": "1\" 1'"},
        {"material_description": "NONPIPE - partial missing inch", "quantity": "2' 13"},
        {"material_description": "NONPIPE - partial missing feet", "quantity": '2 13\"'},
        {"material_description": "NONPIPE - normalize inch test", "quantity": '2\"'},
        {"material_description": "OTHER NONPIPE - Pipe SUPPORT - ignore term", "quantity": "5"},
        {"material_description": "OTHER NONPIPE - Pipe BEND - ignore term", "quantity": "5"},
    ]

    test_df = pl.DataFrame(data, strict=False)

    # test_df = pl.read_excel(r"C:\Drawings\Clients\axisindustries\05-02-25\Binder1\data\1403 pages OCR BOM Data.xlsx")

    # Validate the dataframe
    import time
    start = time.perf_counter()
    validated_df = validate_dataframe(test_df)
    end = time.perf_counter()
    print(f"Time taken: {end - start}")
    print(validated_df)
    print("\nValidated DataFrame:")
    print(validated_df.select(['material_description', 'quantity', '_audit_is_pipe_item',
                        '_audit_is_valid', '_audit_normalized_value', '_audit_severity', '_audit_validation_issues']))

    # Show rows that need review (severity > 0)
    review_df = validated_df.filter(pl.col("_audit_severity") > 0)
    print("\nRows that need review:")

    with pl.Config(tbl_cols=-1, tbl_rows=100, fmt_str_lengths=100):
        print(review_df.select(['material_description', 'quantity', '_audit_severity', '_audit_validation_issues', '_audit_normalized_value']))