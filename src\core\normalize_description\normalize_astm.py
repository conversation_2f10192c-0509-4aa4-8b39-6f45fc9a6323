# from unit_tests.normalize_description import normalize_schedule
import re
import pandas as pd

from src.core.categorization_table import categorization_table

DEBUG_FILE = False

'''
Ignore values that are followed by measurement units
Ignore combining values if it makes up a Suffix and is part of a word more than 2 in length
Fix rating match_type tage (currently is the rating value)
Do not match with PN ###
Need better Flagging for values that do not match exact "A, B, F" ASTM prefixes
'''

# official_astm_set = set(official_astm_list)

def get_all_options_as_set(categorization_table, field_name):
    """
    Retrieve the options list for a given field name and format it as a Python set.

    Parameters:
        categorization_table (list): A list of dictionaries containing field, description, and options.
        field_name (str): The name of the field for which options are to be retrieved.

    Returns:
        set: A set of options for the specified field, or an empty set if the field is not found.

    Example Usage:
        VALID_ASTM = get_all_options_as_set(categorization_table, "astm")
    """
    for entry in categorization_table:
        if entry["field"] == field_name:
            return set(entry.get("options", []))  # Return the options as a set
    return set()  # Return an empty set if the field is not found


official_astm_set = get_all_options_as_set(categorization_table, "astm") # Get ASTM values from the categorization table
grade_set = get_all_options_as_set(categorization_table, "grade") # Get grade values from the categorization table

pattern = r"ASTM\s+([A-Za-z0-9]+)(?:[\-/\s]+([A-Za-z0-9]+))?(?:\s*GR(?:ADE)?\.?\s*([A-Za-z0-9\-\/]+))?"
WORDS_TO_DROP = {"CLASS","AND"}
GRADE_PREFIXES = ("GR","GRADE","GR.")

# Terms that might be mistaken for grades but should be excluded
GRADE_EXCLUDE_TERMS = {"GRAPHITE", "GRA-PHIL", "A-PHIL", "APHITE"}

def create_astm_tags(description):
    tags, match_types, review = parse_astm_and_grade(description)

    # Add review tag for compound grades that aren't in our standard list
    if tags and 'grade:' in tags:
        # Extract the grade value
        grade_parts = tags.split(', ')
        for part in grade_parts:
            if part.startswith('grade:'):
                grade_value = part[6:]  # Extract the grade value after "grade:"
                if ('/' in grade_value or '-' in grade_value) and grade_value not in grade_set:
                    if not review:
                        review = f"review:Compound grade '{grade_value}' not in standard list"
                    elif f"Compound grade '{grade_value}' not in standard list" not in review:
                        review += f", review:Compound grade '{grade_value}' not in standard list"

    return tags, match_types, review

def normalize_grade_text(text):
    # Normalize grade by removing all non-alphanumeric characters and uppercasing
    return re.sub(r'[^A-Za-z0-9]+', '', text.upper())

# Create a normalized lookup map for grades to handle spacing/dashes issues
normalized_grade_map = {normalize_grade_text(g): g for g in grade_set}

def refine_grade(grade_candidate):
    # Debug flag for compound grades
    DEBUG_COMPOUND = False

    # Attempt exact match first
    if grade_candidate in grade_set:
        return grade_candidate
    if DEBUG_COMPOUND:
        print(f" ->REFINE GRADE: {grade_candidate}")

    # Check if this is a compound grade with '/' or '-'
    if ('/' in grade_candidate or '-' in grade_candidate) and not grade_candidate.startswith('GR'):
        if DEBUG_COMPOUND:
            print(f"[COMPOUND REFINE] Detected potential compound grade: '{grade_candidate}'")

        # Check if the first part before the delimiter is a valid grade
        delimiter = '/' if '/' in grade_candidate else '-'
        parts = grade_candidate.split(delimiter, 1)
        first_part = parts[0]

        if first_part in grade_set:
            if DEBUG_COMPOUND:
                print(f"[COMPOUND REFINE] First part '{first_part}' is a valid grade, preserving full compound: '{grade_candidate}'")
            # First part is valid, return the full compound grade
            return grade_candidate

        # Check if normalized first part is valid
        norm_first = normalize_grade_text(first_part)
        if norm_first in normalized_grade_map:
            if DEBUG_COMPOUND:
                print(f"[COMPOUND REFINE] Normalized first part '{first_part}' matches '{normalized_grade_map[norm_first]}', preserving full compound: '{grade_candidate}'")
            # First part is valid after normalization, return the full compound grade
            return grade_candidate

    # Attempt normalized match
    norm_candidate = normalize_grade_text(grade_candidate)
    if norm_candidate in normalized_grade_map:
        # Found a match in normalized form
        return normalized_grade_map[norm_candidate]

    # Handle special case for grades with GR. prefix
    gr_match = re.match(r'(?:GR\.?|GRADE)\s*([A-Z0-9/-]+)', grade_candidate, re.IGNORECASE)
    if gr_match:
        grade_value = gr_match.group(1).strip()
        # Try direct match with the extracted value
        if grade_value in grade_set:
            return grade_value
        # Try normalized match
        norm_gr_value = normalize_grade_text(grade_value)
        if norm_gr_value in normalized_grade_map:
            return normalized_grade_map[norm_gr_value]

    # Handle compound grades with suffixes (e.g., WP304/304L-W, WP304/304L-S)
    compound_match = re.match(r'([A-Z0-9]+/[A-Z0-9]+)[-]([A-Z0-9]+)', grade_candidate, re.IGNORECASE)
    if compound_match:
        base_grade = compound_match.group(1)
        suffix = compound_match.group(2)

        # Check if the base grade is in our set
        for valid_grade in grade_set:
            if valid_grade.upper() == base_grade.upper() or base_grade.upper() in valid_grade.upper():
                # If suffix is just a single character like 'W' or 'S', append it
                if len(suffix) == 1:
                    return f"{valid_grade}-{suffix}"
                return valid_grade

    # If exact match fails, try splitting on '/', '-', and also re-check normalized parts
    if grade_candidate:
        try:
            # First check if the entire string with slashes or hyphens is a valid grade or close to one
            for valid_grade in grade_set:
                # Check for exact match with normalized forms
                if normalize_grade_text(valid_grade) == normalize_grade_text(grade_candidate):
                    return valid_grade

                # Check for significant overlap
                if (len(valid_grade) >= 4 and len(grade_candidate) >= 4) and \
                   (valid_grade.upper() in grade_candidate.upper() or \
                    grade_candidate.upper() in valid_grade.upper()):
                    # For cases like WPB-S where WPB is in our list
                    if '-' in grade_candidate and not '-' in valid_grade:
                        base_part = grade_candidate.split('-')[0]
                        if normalize_grade_text(base_part) == normalize_grade_text(valid_grade):
                            return grade_candidate  # Return the full value with suffix
                    return valid_grade

            # Handle special case for hyphenated values with suffixes (e.g., WPB-S, WPB-W)
            hyphen_match = re.match(r'([A-Z0-9]+)-([A-Z0-9]+)', grade_candidate, re.IGNORECASE)
            if hyphen_match:
                base_part = hyphen_match.group(1)
                suffix = hyphen_match.group(2)

                # Check if the base part is in our set
                for valid_grade in grade_set:
                    if valid_grade.upper() == base_part.upper() or \
                       normalize_grade_text(valid_grade) == normalize_grade_text(base_part):
                        # If suffix is just a single character like 'W' or 'S', include it
                        if len(suffix) == 1:
                            return f"{valid_grade}-{suffix}"
                        return valid_grade

            # If no direct match, try splitting and matching parts
            parts = re.split(r'[/-]+', grade_candidate)
            recognized_parts = []
            for p in parts:
                if not p:  # Skip empty parts
                    continue

                if p in grade_set:
                    recognized_parts.append(p)
                else:
                    # Check normalized map for each part
                    np = normalize_grade_text(p)
                    if np in normalized_grade_map:
                        recognized_parts.append(normalized_grade_map[np])
                    else:
                        # Try to find a partial match in the grade set
                        found_match = False
                        for valid_grade in grade_set:
                            if (len(p) >= 2 and p.upper() in valid_grade.upper()) or \
                               (len(valid_grade) >= 2 and valid_grade.upper() in p.upper()):
                                recognized_parts.append(valid_grade)
                                found_match = True
                                break

                        if not found_match and len(p) > 1:  # Only keep non-trivial parts
                            # If no match found for this part, keep it as is
                            recognized_parts.append(p)

            if recognized_parts:
                # Check if we have a suffix to preserve (like -W or -S)
                if grade_candidate.endswith('-W') or grade_candidate.endswith('-S'):
                    suffix = grade_candidate[-2:]
                    return "/".join(recognized_parts) + suffix
                # Join recognized parts with '/'
                return "/".join(recognized_parts)
        except Exception as e:
            print(f"Error refining Grade Candidate '{grade_candidate}': {e}")

    # No direct or refined match found yet.
    # Try substring matching of known grades, checking longest first.
    sorted_grades = sorted(grade_set, key=len, reverse=True)
    upper_candidate = grade_candidate.upper()
    for g in sorted_grades:
        # Check if g is fully contained in the candidate (case-insensitive)
        if g.upper() in upper_candidate:
            if DEBUG_FILE:
                print(f"DEBUG: Found partial match for '{grade_candidate}' using substring: {g}")
            return g

    # No match found, return original candidate (fallback)
    return grade_candidate

def remove_grade_prefix_from_code(astm_code):
    # If astm_code contains GR, GRADE or GR. inside it (rare if pattern matched),
    # remove everything from GR onwards.
    upper_code = astm_code.upper()
    for prefix in ["GR.", "GRADE", "GR"]:
        idx = upper_code.find(prefix)
        if idx != -1:
            # Remove from prefix onward
            return astm_code[:idx].strip()
    return astm_code

def extract_first_gr_grade(description):
    """
    Extract the first explicitly prefixed GR grade from the description.
    Excludes known false positives like "GRAPHITE".
    """
    match = re.search(r"\bGR(?:ADE)?\.?\s*([A-Z0-9/-]+)", description, re.IGNORECASE)
    if match:
        grade_value = match.group(1).strip()

        # Check if the extracted value or the text following "GR" should be excluded
        for exclude_term in GRADE_EXCLUDE_TERMS:
            # Check if the extracted value is part of an excluded term
            if exclude_term.upper() in description[match.start():].upper():
                return None

            # Also check if the grade value itself matches an excluded term
            if grade_value.upper() == exclude_term.upper() or \
               (len(grade_value) >= 3 and exclude_term.upper().startswith(grade_value.upper())):
                return None

        return grade_value
    return None

def find_grade_after_prefix(description):
    prefix_match = re.search(r"(GR(?:ADE)?\.?)\s+", description, re.IGNORECASE)
    if not prefix_match:
        return None

    start_idx = prefix_match.end()
    remainder = description[start_idx:]
    tokens = re.split(r"[^A-Za-z0-9]+", remainder.strip())
    tokens = [t for t in tokens if t]

    if not tokens:
        return None

    # Attempt all combinations of consecutive tokens:
    # For each start token, progressively add next tokens without spaces
    # e.g., ["TP","304L"] -> "TP", then "TP304L"
    # If direct match or refine_grade match fails, we attempt alternate forms.

    best_candidate = None
    found_grade = None

    # Add debug flag for compound grades
    DEBUG_COMPOUND = True

    for start in range(len(tokens)):
        candidate = ""
        for end in range(start, len(tokens)):
            candidate += tokens[end]  # Direct concatenation (e.g., "TP" + "304L" -> "TP304L")
            # Check direct candidate
            if candidate in grade_set:
                # Found a match, but check if next token contains '/' or '-' to extend the grade
                if end + 1 < len(tokens) and ('/' in tokens[end+1] or '-' in tokens[end+1]):
                    if DEBUG_COMPOUND:
                        print(f"\n[COMPOUND GRADE] Found initial match '{candidate}' but detected compound delimiter in next token '{tokens[end+1]}'")

                    # Try to extend the grade to include the compound part
                    extended_candidate = candidate
                    for ext_end in range(end + 1, min(end + 4, len(tokens))):
                        extended_candidate += tokens[ext_end]
                        if DEBUG_COMPOUND:
                            print(f"[COMPOUND GRADE] Extended to: '{extended_candidate}'")

                    # Return the extended candidate even if it's not in our grade set
                    if '/' in extended_candidate or '-' in extended_candidate:
                        if DEBUG_COMPOUND:
                            print(f"[COMPOUND GRADE] Using extended compound grade: '{extended_candidate}'")
                        return extended_candidate

                return candidate

            # Check refined candidate
            refined_candidate = refine_grade(candidate)
            if refined_candidate in grade_set:
                # Found a match, but check if next token contains '/' or '-' to extend the grade
                if end + 1 < len(tokens) and ('/' in tokens[end+1] or '-' in tokens[end+1]):
                    if DEBUG_COMPOUND:
                        print(f"\n[COMPOUND GRADE] Found refined match '{refined_candidate}' but detected compound delimiter in next token '{tokens[end+1]}'")

                    # Try to extend the grade to include the compound part
                    extended_candidate = candidate
                    for ext_end in range(end + 1, min(end + 4, len(tokens))):
                        extended_candidate += tokens[ext_end]
                        if DEBUG_COMPOUND:
                            print(f"[COMPOUND GRADE] Extended to: '{extended_candidate}'")

                    # Return the extended candidate even if it's not in our grade set
                    if '/' in extended_candidate or '-' in extended_candidate:
                        if DEBUG_COMPOUND:
                            print(f"[COMPOUND GRADE] Using extended compound grade: '{extended_candidate}'")
                        return extended_candidate

                return refined_candidate

            # Not found yet, continue adding tokens

        # If no match found for this start, remember the last refined candidate as fallback
        fallback_candidate = refine_grade(candidate)
        if not found_grade:
            best_candidate = fallback_candidate if fallback_candidate else candidate

    # If no exact match found at all, try additional heuristics:
    # Some grades might need manual merging. If best_candidate not in grade_set:
    if best_candidate and best_candidate not in grade_set:
        # Attempt merging tokens differently:
        # For example, if best_candidate came out as "TP/304L" from refine_grade (slash combined parts),
        # try without slash: "TP304L"
        parts = re.split(r"[/-]+", best_candidate)
        if len(parts) > 1:
            # Try concatenating without any delimiter:
            merged = "".join(parts)
            if merged in grade_set:
                return merged
            merged_refined = refine_grade(merged)
            if merged_refined in grade_set:
                return merged_refined
    if DEBUG_FILE:
        print(f"find_grade_after_prefix (BEST CANDIDATE): {best_candidate}")
    return best_candidate

def clean_astm_and_grade(astm_code, grade):
    # Check if grade is already recognized
    grade_already_recognized = (grade in grade_set)

    # Remove undesired words from astm_code
    for w in WORDS_TO_DROP:
        astm_code = re.sub(rf"{w}", "", astm_code, flags=re.IGNORECASE)
    astm_code = astm_code.strip()

    # If we already have a recognized grade, do not attempt to append more
    if grade_already_recognized:
        return astm_code, grade

    for sep in ['-', '/']:
        if sep in astm_code:
            left, right = astm_code.split(sep, 1)
            if left.upper() in official_astm_set:
                if right and right.upper() not in {"GR","GRADE","GR."}:
                    # Only append right if it’s recognized as a grade
                    if right in grade_set:
                        grade = (grade or "") + right
                    else:
                        refined_right = refine_grade(right)
                        if refined_right in grade_set:
                            grade = (grade or "") + refined_right
                        # If still not recognized, don't append
                return left, grade

    upper_code = astm_code.upper()
    longest_prefix = ""
    for code in official_astm_set:
        if upper_code.startswith(code):
            if len(code) > len(longest_prefix):
                longest_prefix = code

    if longest_prefix and longest_prefix != upper_code:
        remainder = astm_code[len(longest_prefix):]
        remainder = remainder.strip("-_/ ")
        if remainder and remainder.upper() not in {"GR","GRADE","GR."}:
            # Only append remainder if it's a known or refinable grade
            if remainder in grade_set:
                grade = (grade or "") + remainder
            else:
                refined_remainder = refine_grade(remainder)
                if refined_remainder in grade_set:
                    grade = (grade or "") + refined_remainder
                # If not recognized, don't append remainder to grade
        return longest_prefix, grade

    return astm_code, grade

def parse_astm_and_grade(desc):
    match = re.search(pattern, desc, re.IGNORECASE)
    astm_code = None
    grade = None
    review = []

    # Debug flag for compound grades
    DEBUG_COMPOUND = True

    # First, try to find combined ASTM/Grade patterns like "TP304L/304L"
    # Improved pattern to better capture compound grades
    combined_pattern = r'\b([A-Z]+\d+[A-Z]*(?:/[A-Z0-9]+[A-Z]*)+)\b'
    combined_match = re.search(combined_pattern, desc, re.IGNORECASE)
    if combined_match:
        combined_value = combined_match.group(1)
        if DEBUG_COMPOUND:
            print(f"[COMPOUND PATTERN] Found compound grade pattern: '{combined_value}'")

        # Check if this is a valid grade
        found_match = False
        for g in grade_set:
            if g.upper() == combined_value.upper() or \
               (len(g) >= 5 and len(combined_value) >= 5 and \
                (g.upper() in combined_value.upper() or combined_value.upper() in g.upper())):
                grade = g
                found_match = True
                if DEBUG_COMPOUND:
                    print(f"[COMPOUND PATTERN] Matched to existing grade: '{g}'")
                break

        # If no match found but it's a valid compound format, use it as is
        if not found_match and '/' in combined_value:
            grade = combined_value
            if DEBUG_COMPOUND:
                print(f"[COMPOUND PATTERN] No match found, using as is: '{grade}'")

        # If we found a grade but no ASTM yet, look for ASTM in the first part
        if grade and not astm_code:
            first_part = combined_value.split('/')[0]
            # Extract potential ASTM code from the first part
            astm_match = re.match(r'([A-Z]+)(\d+)', first_part, re.IGNORECASE)
            if astm_match:
                letter_prefix = astm_match.group(1).upper()
                number = astm_match.group(2)
                potential_astm = f"{letter_prefix}{number}"

                # Check if this is a valid ASTM code
                if potential_astm.upper() in official_astm_set:
                    astm_code = potential_astm.upper()
                # Also check if just the letter+number is valid (e.g., A106 from TP106)
                elif letter_prefix[0] + number in official_astm_set:
                    astm_code = letter_prefix[0] + number

    if match:
        main_code = match.group(1)
        appended_code = match.group(2)
        pattern_grade = match.group(3)

        if appended_code:
            appended_code = appended_code.replace(" ", "")

        # Determine how to handle appended_code
        if appended_code:
            combined_astm = (main_code + appended_code).upper()
            if combined_astm in official_astm_set:
                # appended_code is part of the ASTM code
                astm_code = main_code + appended_code
                appended_code = None
            else:
                # Check if main_code alone is a known ASTM
                if main_code.upper() in official_astm_set:
                    # appended_code likely belongs to the grade, not the astm code
                    astm_code = main_code
                    # appended_code will be used as first token in grade search
                else:
                    # Fallback to original logic if needed
                    astm_code = main_code + appended_code
                    appended_code = None
        else:
            astm_code = main_code

        clean_code = remove_grade_prefix_from_code(astm_code)
        if clean_code != astm_code:
            astm_code = clean_code
            grade = find_grade_after_prefix(desc)
            if DEBUG_FILE:
                print(f"parse_astm_and_grade 0 : {grade}")
        else:
            if pattern_grade and pattern_grade.upper() not in {"GR","GRADE","GR."}:
                grade = pattern_grade.strip()
            else:
                # Attempt to find grade from subsequent tokens if appended_code exists
                if appended_code:
                    if DEBUG_FILE:
                        print("DEBUG: No pattern_grade identified or not usable. Attempting to find grade from following tokens...")
                    desc_after_astm = desc[match.end():]
                    tokens_after = re.split(r"[^A-Za-z0-9]+", desc_after_astm.strip())
                    tokens_after = [t for t in tokens_after if t]

                    # Limit search distance if we have a recognized ASTM code
                    if astm_code and astm_code.upper() in official_astm_set:
                        max_search_distance = 5
                        tokens_after = tokens_after[:max_search_distance]
                    if DEBUG_FILE:
                        print(f"DEBUG: Tokens after ASTM code: {tokens_after}")

                    # appended_code is treated as the start of grade searching if it's leftover
                    all_tokens = [appended_code] + tokens_after
                    found_grade_candidate = False
                    # Longest match first
                    for length in range(len(all_tokens), 0, -1):
                        for start in range(0, len(all_tokens) - length + 1):
                            candidate_parts = all_tokens[start:start+length]
                            candidate_str = "".join(candidate_parts)
                            if candidate_str in grade_set:
                                grade = candidate_str
                                if DEBUG_FILE:
                                    print(f"DEBUG: Found long-match grade candidate: {grade}")
                                found_grade_candidate = True
                                break
                            refined_test = refine_grade(candidate_str)
                            if refined_test in grade_set:
                                grade = refined_test
                                if DEBUG_FILE:
                                    print(f"DEBUG: Found long-match refined grade candidate: {grade}")
                                found_grade_candidate = True
                                break
                        if found_grade_candidate:
                            break

                    if not found_grade_candidate:
                        # fallback old logic (limited distance already applied)
                        for t in tokens_after:
                            test_candidate = appended_code + t
                            if test_candidate in grade_set:
                                grade = test_candidate
                                if DEBUG_FILE:
                                    print(f"DEBUG: Found combined grade candidate: {grade}")
                                found_grade_candidate = True
                                break
                            refined_test = refine_grade(test_candidate)
                            if refined_test in grade_set:
                                grade = refined_test
                                if DEBUG_FILE:
                                    print(f"DEBUG: Found refined combined grade candidate: {grade}")
                                found_grade_candidate = True
                                break
                            if t in grade_set:
                                grade = t
                                if DEBUG_FILE:
                                    print(f"DEBUG: Found separate grade candidate: {grade}")
                                found_grade_candidate = True
                                break

                        if not found_grade_candidate:
                            if DEBUG_FILE:
                                print("DEBUG: No suitable grade found after attempting longest and incremental tokens.")

        astm_code, grade = clean_astm_and_grade(astm_code, grade)

        if astm_code.upper() in official_astm_set:
            astm_tag = f"astm:{astm_code.upper()}"
            astm_mt = "astm:Exact match"
        else:
            astm_tag = f"astm:{astm_code.upper()}"
            astm_mt = "astm:Fallback"
            if "review:Not in ASTM list" not in review:
                review.append("review:Not in ASTM list")

        tags = [astm_tag]
        match_types = [astm_mt]

        if grade and grade.upper() not in {"GR","GRADE","GR."}:
            if DEBUG_FILE:
                print(f"\n\n---------------\nparse_astm_and_grade 1 : {grade}")
            refined = refine_grade(grade)
            if refined and refined.upper() not in {"GR","GRADE","GR."}:
                tags.append(f"grade:{refined}")
                match_types.append("grade:Fallback")
                if refined not in grade_set:
                    review.append("review:Not in grade list")

        return ", ".join(tags), ", ".join(match_types), ", ".join(review)

    # Fallback logic unchanged
    tokens = re.split(r"[^A-Za-z0-9\-]+", desc.upper())
    tokens = [t for t in tokens if t]

    astm_code = None
    grade = None
    review = []
    i = 0
    while i < len(tokens):
        tk = tokens[i]
        if tk.startswith("GR"):
            grade_candidate = find_grade_after_prefix(desc)
            grade_candidate = refine_grade(grade_candidate) if grade_candidate else None
            if DEBUG_FILE:
                print(f"parse_astm_and_grade 2 : {grade_candidate}")
            if grade_candidate and grade_candidate.upper() not in {"GR","GRADE","GR."}:
                grade = grade_candidate
            break

        if tk in official_astm_set:
            astm_code = tk
            i += 1
            if i < len(tokens) and tokens[i].startswith("GR"):
                grade_candidate = find_grade_after_prefix(desc)
                grade_candidate = refine_grade(grade_candidate) if grade_candidate else None
                if DEBUG_FILE:
                    print(f"parse_astm_and_grade 3 : {grade_candidate}")
                if grade_candidate and grade_candidate.upper() not in {"GR","GRADE","GR."}:
                    grade = grade_candidate
            break
        elif tk.isdigit() and ("A"+tk) in official_astm_set:
            astm_code = "A"+tk
            i += 1
            if i < len(tokens) and tokens[i].startswith("GR"):
                grade_candidate = find_grade_after_prefix(desc)
                grade_candidate = refine_grade(grade_candidate) if grade_candidate else None
                if DEBUG_FILE:
                    print(f"parse_astm_and_grade 4 : {grade_candidate}")
                if grade_candidate and grade_candidate.upper() not in {"GR","GRADE","GR."}:
                    grade = grade_candidate
            break
        i += 1

    if not astm_code and not grade:
        # First try the simple extraction method
        grade_candidate = extract_first_gr_grade(desc)
        if not grade_candidate:
            # Fall back to the more complex method if simple extraction fails
            grade_candidate = find_grade_after_prefix(desc)

        grade_candidate = refine_grade(grade_candidate) if grade_candidate else None
        if grade_candidate and grade_candidate.upper() not in {"GR","GRADE","GR."}:
            grade = grade_candidate

    if not astm_code:
        # If "ASTM" in tokens, fallback
        if "ASTM" in tokens:
            astm_index = tokens.index("ASTM")
            if astm_index+1 < len(tokens):
                candidate = tokens[astm_index+1]
                astm_code = candidate
                if candidate.upper() not in official_astm_set:
                    review.append("review:Not in ASTM list")

    if astm_code:
        astm_code, grade = clean_astm_and_grade(astm_code, grade)

    tags = []
    match_types = []

    if astm_code:
        if astm_code.upper() in official_astm_set:
            tags.append(f"astm:{astm_code.upper()}")
            match_types.append("astm:Exact match")
        else:
            tags.append(f"astm:{astm_code.upper()}")
            match_types.append("astm:Fallback")
            if "review:Not in ASTM list" not in review:
                review.append("review:Not in ASTM list")

    if grade and grade.upper() not in {"GR","GRADE","GR."}:
        refined = refine_grade(grade)
        if refined and refined.upper() not in {"GR","GRADE","GR."}:
            tags.append(f"grade:{refined}")
            match_types.append("grade:Fallback")
            if refined not in grade_set:
                review.append("review:Not in grade list")

    return ", ".join(tags), ", ".join(match_types), ", ".join(review)

if __name__ == "__main__":

    # Example usage:
    df = pd.read_excel(r"unit_tests\test_files\astm tests.xlsx")

    # Apply the create_tags function and convert result to three columns
    output_df = df['material_description'].apply(lambda x: pd.Series(create_astm_tags(x)))
    output_df.columns = ['Tags', 'Match Types', 'Review']

    # Concatenate the original DataFrame with the new columns
    final_df = pd.concat([df, output_df], axis=1)
    # Ensure ASTM column is always uppercase, handle blanks/numbers/None
    def astm_format(val):
        if val is None:
            return None
        if isinstance(val, str):
            val = val.strip()
            return val.upper() if val else None
        return val  # numbers and other types unchanged

    if 'astm' in final_df.columns:
        final_df['astm'] = final_df['astm'].apply(astm_format)

    # Save the final result
    final_df.to_excel(r"unit_tests\test_files\output\normalized_data.xlsx")

    # Display the resulting DataFrame
    # print(final_df)
    print(final_df.to_markdown())

    # Set no limits on display
    pd.set_option('display.max_columns', None)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.width', None)
    # For rows containing either "TP" or "WP"
    # filtered_df = final_df[final_df.astype(str).apply(lambda x: x.str.contains('TP|WP', na=False)).any(axis=1)]

    # # Print in markdown format
    # print("\n\nFiltered DF:")
    # print(filtered_df.to_markdown())

    # Display the final table
    # print(final_df.to_markdown(index=False))



