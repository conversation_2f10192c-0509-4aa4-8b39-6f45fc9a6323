"""
Comprehensive strategies to improve model accuracy for ATEM Classifier

This script provides multiple approaches to enhance the model performance
based on the accuracy analysis.
"""

import pandas as pd
import numpy as np

def analyze_data_quality(file_path):
    """
    Analyze data quality and suggest improvements
    """
    df = pd.read_excel(file_path)

    print("=== DATA QUALITY ANALYSIS ===")

    # Check for missing values
    missing_data = df.isnull().sum()
    print(f"Missing values per column:")
    for col, missing in missing_data.items():
        if missing > 0:
            print(f"  {col}: {missing} ({missing/len(df)*100:.1f}%)")

    # Check label distribution
    label_cols = [
        "rfq_scope", "general_category", "unit_of_measure", "material",
        "abbreviated_material", "astm", "grade", "rating", "schedule",
        "coating", "forging", "ends", "pipe_category", "valve_type",
        "fitting_category", "weld_category", "bolt_category", "gasket_category"
    ]

    print(f"\n=== LABEL DISTRIBUTION ANALYSIS ===")
    for col in label_cols:
        if col in df.columns:
            unique_vals = df[col].value_counts()
            print(f"\n{col}:")
            print(f"  Unique values: {len(unique_vals)}")
            print(f"  Most common: {unique_vals.head(3).to_dict()}")

            # Check for imbalance
            if len(unique_vals) > 1:
                ratio = unique_vals.iloc[0] / unique_vals.iloc[1] if len(unique_vals) > 1 else 1
                if ratio > 10:
                    print(f"  ⚠️  HIGHLY IMBALANCED (ratio: {ratio:.1f}:1)")
                elif ratio > 5:
                    print(f"  ⚠️  Imbalanced (ratio: {ratio:.1f}:1)")

def suggest_data_improvements():
    """
    Suggest data collection and preprocessing improvements
    """
    print("\n=== DATA IMPROVEMENT SUGGESTIONS ===")

    improvements = [
        "1. **Collect More Data for Rare Labels**",
        "   - Focus on underrepresented categories",
        "   - Aim for at least 50-100 samples per label value",
        "",
        "2. **Data Augmentation**",
        "   - Paraphrase existing descriptions",
        "   - Add synonyms for technical terms",
        "   - Create variations with different formatting",
        "",
        "3. **Feature Engineering**",
        "   - Extract numerical values (sizes, pressures, etc.)",
        "   - Standardize units and formats",
        "   - Create hierarchical labels (e.g., material -> submaterial)",
        "",
        "4. **Data Cleaning**",
        "   - Standardize abbreviations (CS vs Carbon Steel)",
        "   - Fix typos and inconsistencies",
        "   - Remove or fix incomplete entries",
        "",
        "5. **Label Hierarchy**",
        "   - Group similar labels together",
        "   - Create parent-child relationships",
        "   - Use multi-level classification"
    ]

    for improvement in improvements:
        print(improvement)

def suggest_model_improvements():
    """
    Suggest model architecture and training improvements
    """
    print("\n=== MODEL IMPROVEMENT STRATEGIES ===")

    strategies = [
        "1. **Model Architecture**",
        "   - Try different base models: RoBERTa, DistilBERT, DeBERTa",
        "   - Use domain-specific models (SciBERT for technical text)",
        "   - Experiment with model sizes (base vs large)",
        "",
        "2. **Training Strategies**",
        "   - Increase training epochs (10-20)",
        "   - Use learning rate scheduling",
        "   - Implement early stopping",
        "   - Try different optimizers (AdamW, RAdam)",
        "",
        "3. **Loss Functions**",
        "   - Focal Loss for imbalanced classes",
        "   - Asymmetric Loss for multi-label",
        "   - Custom weighted loss per label",
        "",
        "4. **Ensemble Methods**",
        "   - Train multiple models with different seeds",
        "   - Combine predictions from different architectures",
        "   - Use voting or averaging strategies",
        "",
        "5. **Post-processing**",
        "   - Optimize thresholds per label",
        "   - Apply business rules and constraints",
        "   - Use label dependencies and hierarchies"
    ]

    for strategy in strategies:
        print(strategy)

def create_threshold_optimization_script():
    """
    Create a script to optimize prediction thresholds
    """
    script_content = '''
def optimize_thresholds(y_true, y_pred_proba, metric='f1'):
    """
    Optimize prediction thresholds for each label individually
    """
    from sklearn.metrics import f1_score, precision_score, recall_score
    import numpy as np

    n_labels = y_true.shape[1]
    optimal_thresholds = []

    for i in range(n_labels):
        best_threshold = 0.5
        best_score = 0

        # Try different thresholds
        for threshold in np.arange(0.1, 0.9, 0.05):
            y_pred_binary = (y_pred_proba[:, i] > threshold).astype(int)

            if metric == 'f1':
                score = f1_score(y_true[:, i], y_pred_binary, zero_division=0)
            elif metric == 'precision':
                score = precision_score(y_true[:, i], y_pred_binary, zero_division=0)
            elif metric == 'recall':
                score = recall_score(y_true[:, i], y_pred_binary, zero_division=0)

            if score > best_score:
                best_score = score
                best_threshold = threshold

        optimal_thresholds.append(best_threshold)

    return optimal_thresholds
'''

    with open('run/threshold_optimization.py', 'w') as f:
        f.write(script_content)

    print("Created threshold_optimization.py")

def main():
    """
    Main function to run all analyses and suggestions
    """
    file_path = "src/training/data/rfq_template-example.xlsx"

    # Run analyses
    analyze_data_quality(file_path)
    suggest_data_improvements()
    suggest_model_improvements()
    create_threshold_optimization_script()

    print("\n=== IMMEDIATE ACTION ITEMS ===")
    print("1. Run the updated transformers_train.py with improved parameters")
    print("2. Collect more data for labels with <50% accuracy")
    print("3. Try different base models (RoBERTa, DeBERTa)")
    print("4. Implement threshold optimization")
    print("5. Consider data augmentation for rare labels")

if __name__ == "__main__":
    main()
