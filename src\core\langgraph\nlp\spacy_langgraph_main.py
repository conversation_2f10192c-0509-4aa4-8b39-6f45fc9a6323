"""
SpaCy-based LangGraph Workflow for BOM Classification

Replaces LLM-based workflow with SpaCy NER models while maintaining the same workflow structure
"""

from typing import Optional
from langgraph.graph import StateGraph, END

# Import from existing codebase
from state_models import ClassificationState, create_initial_state
from nlp.spacy_classification_nodes import (
    material_analysis_node,
    pipe_classification_node,
    fitting_classification_node,
    valve_classification_node,
    flange_classification_node
)

def route_to_category_node(state: ClassificationState) -> str:
    """
    Route to the appropriate category node based on processing path

    Args:
        state: Current workflow state

    Returns:
        Name of the next node to execute
    """
    # Get processing path from state
    processing_path = state.get("processing_path", "unknown")

    # Map processing paths to node names
    category_routing = {
        "pipe": "pipe_classification",
        "fitting": "fitting_classification",
        "valve": "valve_classification",
        "flange": "flange_classification",
        "gasket": "gasket_classification",
        "bolt": "bolt_classification",
        "support": "support_classification",
        "miscellaneous": "miscellaneous_classification",
        "unknown": "miscellaneous_classification"
    }

    # Get next node name
    next_node = category_routing.get(processing_path, "miscellaneous_classification")

    if state.get("debug_mode", False):
        print(f"   🔀 Routing to: {next_node}")

    return next_node

def should_perform_qa(state: ClassificationState) -> str:
    """
    Determine if QA is needed based on confidence scores

    Args:
        state: Current workflow state

    Returns:
        Next node name
    """
    # Check confidence scores
    confidence_scores = state.get("confidence_scores", {})

    # If any confidence score is below threshold, route to QA
    low_confidence = any(score < 0.7 for score in confidence_scores.values())

    if low_confidence:
        return "qa_decisions"
    else:
        return "self_audit"

def create_classification_workflow(
    spacy_model_dir: Optional[str] = None,
    debug_mode: bool = False
) -> StateGraph:
    """
    Create the SpaCy-based classification workflow

    Args:
        spacy_model_dir: Directory containing the SpaCy model
        debug_mode: Enable debug output

    Returns:
        StateGraph for the classification workflow
    """
    # Create workflow
    workflow = StateGraph(ClassificationState)

    # Add nodes
    workflow.add_node("material_analysis", material_analysis_node)
    workflow.add_node("pipe_classification", pipe_classification_node)
    workflow.add_node("fitting_classification", fitting_classification_node)
    workflow.add_node("valve_classification", valve_classification_node)
    workflow.add_node("flange_classification", flange_classification_node)

    # Set entry point
    workflow.set_entry_point("material_analysis")

    # Add conditional edges for category routing
    workflow.add_conditional_edges(
        "material_analysis",
        route_to_category_node,
        {
            "pipe_classification": "pipe_classification",
            "fitting_classification": "fitting_classification",
            "valve_classification": "valve_classification",
            "flange_classification": "flange_classification",
            "miscellaneous_classification": END
        }
    )

    # Add edges from category nodes to QA or end
    for category_node in [
        "pipe_classification",
        "fitting_classification",
        "valve_classification",
        "flange_classification"
    ]:
        workflow.add_edge(category_node, END)

    return workflow
