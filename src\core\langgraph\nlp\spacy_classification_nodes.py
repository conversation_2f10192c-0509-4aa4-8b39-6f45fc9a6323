"""
SpaCy-based Classification Nodes for LangGraph Workflow

Replaces LLM-based nodes with SpaCy NER classification
"""

import asyncio
from typing import Dict, Any, List, Optional
import re

# Import from existing codebase
from src.core.langgraph.state_models import ClassificationState
from src.core.langgraph.nlp.spacy_integration import SpacyModelHandler

# Global SpaCy model handler
_spacy_model_handler = None

def get_or_create_spacy_handler(model_dir: str = None, debug_mode: bool = False) -> SpacyModelHandler:
    """Get or create the global SpaCy model handler"""
    global _spacy_model_handler
    if _spacy_model_handler is None:
        _spacy_model_handler = SpacyModelHandler(model_dir, debug_mode)
    return _spacy_model_handler

async def material_analysis_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 1: Extract key properties using SpaCy NER

    Args:
        state: Current workflow state

    Returns:
        Updated workflow state
    """
    try:
        material_description = state["material_description"]
        item_id = state.get("item_id", "unknown")
        debug_mode = state.get("debug_mode", False)

        if debug_mode:
            print(f"\n🔍 STAGE 1: Material Analysis with SpaCy")
            print(f"   Description: {material_description}")

        # Get SpaCy model handler
        model_dir = state.get("spacy_model_dir")
        spacy_handler = get_or_create_spacy_handler(model_dir, debug_mode)

        # Classify with SpaCy
        classification_result = spacy_handler.classify_item(material_description)

        # Extract results
        extracted_properties = classification_result.get("extracted_properties", {})
        processing_path = classification_result.get("processing_path", "unknown")
        confidence_scores = classification_result.get("confidence_scores", {})

        if debug_mode:
            print(f"   ✅ Category: {processing_path}")
            print(f"   📊 Properties: {extracted_properties}")

        # Update state
        return {
            **state,
            "extracted_properties": extracted_properties,
            "processing_path": processing_path,
            "confidence_scores": confidence_scores,
            "current_stage": "classification",
            "workflow_path": state["workflow_path"] + ["material_analysis"]
        }

    except Exception as e:
        if state.get("debug_mode", False):
            print(f"   ❌ Error in material analysis: {str(e)}")

        # Return state with error
        return {
            **state,
            "error": str(e),
            "current_stage": "error",
            "workflow_path": state["workflow_path"] + ["material_analysis", "error"]
        }

async def pipe_classification_node(state: ClassificationState) -> ClassificationState:
    """
    Specialized node for pipe classification using SpaCy and regex

    Args:
        state: Current workflow state

    Returns:
        Updated workflow state with pipe classifications
    """
    try:
        material_description = state["material_description"]
        extracted_properties = state.get("extracted_properties", {})
        debug_mode = state.get("debug_mode", False)

        if debug_mode:
            print(f"\n🔧 STAGE 2: Pipe Classification with SpaCy")
            print(f"   Description: {material_description}")

        # Extract pipe-specific properties using regex
        pipe_properties = {}

        # Size extraction
        size_pattern = r'(\d+(?:\.\d+)?)(?:\s*[xX]\s*\d+(?:\.\d+)?)?(?:\s*(?:"|INCH|IN))?'
        size_match = re.search(size_pattern, material_description)
        if size_match:
            pipe_properties["size"] = size_match.group(0)

        # Schedule extraction
        schedule_pattern = r'SCH(?:EDULE)?\s*(\d+\w*)'
        schedule_match = re.search(schedule_pattern, material_description, re.IGNORECASE)
        if schedule_match:
            pipe_properties["schedule"] = schedule_match.group(0)

        # ASTM standard extraction
        astm_pattern = r'(?:ASTM\s*)?(?:A\d{1,3}(?:-\d{1,2})?)'
        astm_match = re.search(astm_pattern, material_description, re.IGNORECASE)
        if astm_match:
            pipe_properties["astm_standard"] = astm_match.group(0)

        # Combine with SpaCy extracted properties
        field_classifications = {
            "category": "pipe",
            **pipe_properties,
            **extracted_properties
        }

        if debug_mode:
            print(f"   ✅ Classifications: {field_classifications}")

        # Update state
        return {
            **state,
            "field_classifications": field_classifications,
            "current_stage": "qa_decisions",
            "workflow_path": state["workflow_path"] + ["pipe_classification"]
        }

    except Exception as e:
        if state.get("debug_mode", False):
            print(f"   ❌ Error in pipe classification: {str(e)}")

        # Return state with error
        return {
            **state,
            "error": str(e),
            "current_stage": "error",
            "workflow_path": state["workflow_path"] + ["pipe_classification", "error"]
        }

# Similar implementations for other category nodes (fitting, valve, flange, etc.)