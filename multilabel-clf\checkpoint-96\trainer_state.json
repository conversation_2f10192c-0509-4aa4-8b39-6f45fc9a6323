{"best_global_step": 96, "best_metric": 0.9397018970189702, "best_model_checkpoint": "./multilabel-clf\\checkpoint-96", "epoch": 4.0, "eval_steps": 500, "global_step": 96, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4166666666666667, "grad_norm": 1.4755562543869019, "learning_rate": 2.7e-06, "loss": 1.4433, "step": 10}, {"epoch": 0.8333333333333334, "grad_norm": 1.3411080837249756, "learning_rate": 5.7000000000000005e-06, "loss": 1.4072, "step": 20}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6632444262504578, "eval_per_label_accuracy": 0.5972222222222222, "eval_runtime": 0.2774, "eval_samples_per_second": 346.11, "eval_steps_per_second": 43.264, "step": 24}, {"epoch": 1.25, "grad_norm": 2.584810495376587, "learning_rate": 8.7e-06, "loss": 1.3314, "step": 30}, {"epoch": 1.6666666666666665, "grad_norm": 1.3809592723846436, "learning_rate": 1.1700000000000001e-05, "loss": 1.2472, "step": 40}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.5573007464408875, "eval_per_label_accuracy": 0.8490006775067751, "eval_runtime": 0.2782, "eval_samples_per_second": 345.136, "eval_steps_per_second": 43.142, "step": 48}, {"epoch": 2.0833333333333335, "grad_norm": 1.4139870405197144, "learning_rate": 1.47e-05, "loss": 1.1574, "step": 50}, {"epoch": 2.5, "grad_norm": 1.126013159751892, "learning_rate": 1.77e-05, "loss": 1.066, "step": 60}, {"epoch": 2.9166666666666665, "grad_norm": 1.0250661373138428, "learning_rate": 2.07e-05, "loss": 0.9724, "step": 70}, {"epoch": 3.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.44352662563323975, "eval_per_label_accuracy": 0.9391937669376694, "eval_runtime": 0.2817, "eval_samples_per_second": 340.742, "eval_steps_per_second": 42.593, "step": 72}, {"epoch": 3.3333333333333335, "grad_norm": 0.923151433467865, "learning_rate": 2.37e-05, "loss": 0.8772, "step": 80}, {"epoch": 3.75, "grad_norm": 0.873205840587616, "learning_rate": 2.6700000000000002e-05, "loss": 0.783, "step": 90}, {"epoch": 4.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.3358411490917206, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2775, "eval_samples_per_second": 346.008, "eval_steps_per_second": 43.251, "step": 96}], "logging_steps": 10, "max_steps": 168, "num_input_tokens_seen": 0, "num_train_epochs": 7, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}