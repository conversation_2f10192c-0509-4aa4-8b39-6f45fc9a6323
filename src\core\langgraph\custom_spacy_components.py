"""
Custom SpaCy Components for BOM Classification

Adds specialized components for piping BOM classification
"""

import re
from spacy.language import Language
from spacy.tokens import Doc

# Register custom Doc extensions
if not Doc.has_extension("component_type"):
    Doc.set_extension("component_type", default=None)

if not Doc.has_extension("material_type"):
    Doc.set_extension("material_type", default=None)

@Language.factory("component_classifier")
class ComponentClassifier:
    """Custom SpaCy component to classify piping components"""

    def __init__(self, nlp, name):
        self.nlp = nlp
        self.name = name

        # Component type keywords
        self.component_types = {
            "pipe": {"PIPE", "SMLS", "SEAMLESS", "ERW", "LINE PIPE"},
            "fitting": {"ELBOW", "TEE", "REDUCER", "COUPLING", "NIPPLE", "CAP", "UNION", "WYE"},
            "valve": {"VALVE", "BALL", "GATE", "<PERSON><PERSON><PERSON><PERSON>", "CHECK", "BUTTERFLY", "NEEDLE"},
            "flange": {"FLANGE", "WN", "SO", "BLIND", "RTJ", "RF", "FF"},
            "gasket": {"GASKET", "SPIRAL WOUND", "RING", "RTJ GASKET"},
            "bolt": {"BOLT", "NUT", "STUD", "WASHER", "B7", "2H", "HEX"},
            "support": {"SUPPORT", "HANGER", "CLAMP", "GUIDE", "REST", "SHOE"}
        }

        # Material type keywords
        self.material_types = {
            "carbon_steel": {"CS", "CARBON STEEL", "A105", "A106", "A53", "A234"},
            "stainless_steel": {"SS", "STAINLESS", "316", "304", "A312", "A403"},
            "alloy": {"ALLOY", "CHROME", "MOLY", "F11", "F22", "F91"},
            "plastic": {"PVC", "HDPE", "PP", "CPVC", "PVDF"}
        }

    def __call__(self, doc):
        """Process the document and classify component and material types"""
        # Convert to uppercase for consistent matching
        text_upper = doc.text.upper()

        # Classify component type
        component_type = self._classify_component_type(text_upper)
        doc._.component_type = component_type

        # Classify material type
        material_type = self._classify_material_type(text_upper)
        doc._.material_type = material_type

        return doc

    def _classify_component_type(self, text: str) -> str:
        """Classify the component type based on keywords"""
        for component, keywords in self.component_types.items():
            if any(keyword in text for keyword in keywords):
                return component
        return "unknown"

    def _classify_material_type(self, text: str) -> str:
        """Classify the material type based on keywords"""
        for material, keywords in self.material_types.items():
            if any(keyword in text for keyword in keywords):
                return material
        return "unknown"

@Language.factory("regex_entity_extractor")
class RegexEntityExtractor:
    """Custom SpaCy component to extract entities using regex patterns"""

    def __init__(self, nlp, name):
        self.nlp = nlp
        self.name = name

        # Regex patterns for entity extraction
        self.patterns = {
            "SIZE": r'(\d+(?:\.\d+)?)(?:\s*[xX]\s*\d+(?:\.\d+)?)?(?:\s*(?:"|INCH|IN))?',
            "SCHEDULE": r'SCH(?:EDULE)?\s*(\d+\w*)',
            "ASTM_STANDARD": r'(?:ASTM\s*)?(?:A\d{1,3}(?:-\d{1,2})?)',
            "PRESSURE_CLASS": r'(?:CL|CLASS)?\s*(\d{3,4})(?:#|\s*LB)?',
            "END_CONNECTION": r'(?:BE|PE|BW|SW|THD|THRD|RF|FF|RTJ)'
        }

    def __call__(self, doc):
        """Process the document and extract entities using regex patterns"""
        text = doc.text
        entities = []

        # Extract entities using regex patterns
        for label, pattern in self.patterns.items():
            for match in re.finditer(pattern, text, re.IGNORECASE):
                start, end = match.span()
                entities.append((start, end, label))

        # Sort entities by start position
        entities.sort(key=lambda x: x[0])

        # Create a new doc with the extracted entities
        doc.ents = [doc.char_span(start, end, label=label) for start, end, label in entities]

        return doc