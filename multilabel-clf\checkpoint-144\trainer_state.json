{"best_global_step": 96, "best_metric": 0.9397018970189702, "best_model_checkpoint": "./multilabel-clf\\checkpoint-96", "epoch": 3.0, "eval_steps": 500, "global_step": 144, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.20833333333333334, "grad_norm": 0.7535103559494019, "learning_rate": 1.8000000000000001e-06, "loss": 0.723, "step": 10}, {"epoch": 0.4166666666666667, "grad_norm": 0.732583224773407, "learning_rate": 3.8000000000000005e-06, "loss": 0.7109, "step": 20}, {"epoch": 0.625, "grad_norm": 0.8079116940498352, "learning_rate": 5.8e-06, "loss": 0.6893, "step": 30}, {"epoch": 0.8333333333333334, "grad_norm": 0.6915093660354614, "learning_rate": 7.800000000000002e-06, "loss": 0.6536, "step": 40}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6045021414756775, "eval_per_label_accuracy": 0.7266260162601627, "eval_runtime": 0.2859, "eval_samples_per_second": 335.783, "eval_steps_per_second": 41.973, "step": 48}, {"epoch": 1.0416666666666667, "grad_norm": 0.7783132195472717, "learning_rate": 9.800000000000001e-06, "loss": 0.6192, "step": 50}, {"epoch": 1.25, "grad_norm": 0.6830142140388489, "learning_rate": 1.18e-05, "loss": 0.5825, "step": 60}, {"epoch": 1.4583333333333333, "grad_norm": 0.5799094438552856, "learning_rate": 1.38e-05, "loss": 0.5481, "step": 70}, {"epoch": 1.6666666666666665, "grad_norm": 0.5489576458930969, "learning_rate": 1.58e-05, "loss": 0.5093, "step": 80}, {"epoch": 1.875, "grad_norm": 0.5147504806518555, "learning_rate": 1.7800000000000002e-05, "loss": 0.4728, "step": 90}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.42618370056152344, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2875, "eval_samples_per_second": 333.903, "eval_steps_per_second": 41.738, "step": 96}, {"epoch": 2.0833333333333335, "grad_norm": 0.5360398888587952, "learning_rate": 1.98e-05, "loss": 0.4391, "step": 100}, {"epoch": 2.2916666666666665, "grad_norm": 0.46752867102622986, "learning_rate": 1.8714285714285717e-05, "loss": 0.4051, "step": 110}, {"epoch": 2.5, "grad_norm": 0.44945889711380005, "learning_rate": 1.7285714285714287e-05, "loss": 0.3768, "step": 120}, {"epoch": 2.7083333333333335, "grad_norm": 0.42711904644966125, "learning_rate": 1.5857142857142857e-05, "loss": 0.3549, "step": 130}, {"epoch": 2.9166666666666665, "grad_norm": 0.39985257387161255, "learning_rate": 1.4428571428571429e-05, "loss": 0.332, "step": 140}, {"epoch": 3.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.31474554538726807, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.3004, "eval_samples_per_second": 319.572, "eval_steps_per_second": 39.946, "step": 144}], "logging_steps": 10, "max_steps": 240, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}