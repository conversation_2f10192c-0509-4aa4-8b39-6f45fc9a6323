import os
import spacy
import pandas as pd

from spacy.util import minibatch, compounding
from spacy.training import Example
from sklearn.model_selection import train_test_split

from src.core.normalize_description.normalize_description import normalize_description
from src.core.categorization_table import categorization_table

def create_simple_training_data(df, nlp):
    """Convert dataframe to SpaCy training data format"""
    training_examples = []

    # Get the list of fields we want to extract from categorization_table
    target_fields = [field["field"] for field in categorization_table]

    for _, row in df.iterrows():
        desc = row['normalized_description']
        doc = nlp.make_doc(desc)
        entities = []

        # Track which character spans are already covered by entities
        covered_spans = set()

        # For each field in our training data that matches our target fields
        for field in target_fields:
            if field in row and pd.notna(row[field]):
                field_value = str(row[field])

                # Find the position of this value in the description
                start_pos = desc.lower().find(field_value.lower())

                # Only add if the value is actually found in the text
                if start_pos >= 0:
                    end_pos = start_pos + len(field_value)

                    # Check if this span overlaps with any existing entity
                    span_range = set(range(start_pos, end_pos))
                    if not span_range.intersection(covered_spans):
                        entities.append((start_pos, end_pos, field))
                        # Mark these character positions as covered
                        covered_spans.update(span_range)
                    else:
                        print(f"Skipping overlapping entity: '{field_value}' ({field}) in '{desc}'")

        # Create annotations
        annotations = {"entities": entities}
        example = Example.from_dict(doc, annotations)
        training_examples.append(example)

    return training_examples

def create_span_categorizer_training_data(df, nlp):
    """Convert dataframe to SpaCy training data format

    Prepare data for compatitbility with the span categorizer. Supports overlapping entities.

    """
    training_examples = []

    # Get the list of fields we want to extract from categorization_table
    target_fields = [field["field"] for field in categorization_table]

    for _, row in df.iterrows():
        desc = row['normalized_description']
        doc = nlp.make_doc(desc)
        entities = []

        # Track which character spans are already covered by entities
        covered_spans = set()

        # For each field in our training data that matches our target fields
        for field in target_fields:
            if field in row and pd.notna(row[field]):
                field_value = str(row[field])

                # Find the position of this value in the description
                start_pos = desc.lower().find(field_value.lower())

                # Only add if the value is actually found in the text
                if start_pos >= 0:
                    end_pos = start_pos + len(field_value)

                    # Check if this span overlaps with any existing entity
                    span_range = set(range(start_pos, end_pos))
                    if not span_range.intersection(covered_spans):
                        entities.append((start_pos, end_pos, field))
                        # Mark these character positions as covered
                        covered_spans.update(span_range)
                    else:
                        print(f"Skipping overlapping entity: '{field_value}' ({field}) in '{desc}'")

        # Create annotations
        annotations = {"entities": entities}
        example = Example.from_dict(doc, annotations)
        training_examples.append(example)

    return training_examples

def train_model(training_data, output_dir, n_iter=10):
    """Train a SpaCy NER model"""
    # Create a blank model or load existing
    nlp = spacy.blank("en")

    # Create the pipeline
    if "ner" not in nlp.pipe_names:
        ner = nlp.add_pipe("ner")
    else:
        ner = nlp.get_pipe("ner")

    # Add entity labels from training data
    for example in training_data:
        for ent in example.reference.ents:
            ner.add_label(ent.label_)

    # Disable other pipelines during training
    pipe_exceptions = ["ner", "trf_wordpiecer", "trf_tok2vec"]
    other_pipes = [pipe for pipe in nlp.pipe_names if pipe not in pipe_exceptions]

    # Training loop
    with nlp.disable_pipes(*other_pipes):
        optimizer = nlp.begin_training()

        for i in range(n_iter):
            losses = {}
            batches = minibatch(training_data, size=compounding(4.0, 32.0, 1.001))

            for batch in batches:
                nlp.update(batch, sgd=optimizer, drop=0.35, losses=losses)

            print(f"Iteration {i+1}, Losses: {losses}")

    # Save the model
    if output_dir:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        nlp.to_disk(output_dir)
        print(f"Model saved to {output_dir}")


def train_ner_model(training_file: str, model_dir: str, test_size: float = 0.2):
    """Main function to train simple NER model

    Args:
        training_file (str): Path to the training data file
        model_dir (str): Path to the output model directory
        test_size (float, optional): Proportion of the dataset to include in the validation split. Defaults to 0.2.

    Limitations:
        By default, spaCy does not support overalapping entities.
    """

    print(f"SpaCy version: {spacy.__version__}")
    print("Creating Spacy Model")
    print(f"Output Model directory: {model_dir}")

    nlp = spacy.blank("en")

    print(f"Loading data from {training_file}...")

    # Load the Excel file
    df = pd.read_excel(training_file)
    print(f"Loaded {len(df)} rows of data.")

    # Normalize descriptions
    print("Normalizing descriptions...")
    normalized_results = df['material_description'].apply(normalize_description)
    df['normalized_description'] = normalized_results.apply(lambda x: x[0])

    # Split into training and validation sets
    train_df, val_df = train_test_split(df, test_size=test_size, random_state=42)
    print(f"Training set: {len(train_df)} rows, Validation set: {len(val_df)} rows")

    # Create training data
    print("Creating training data...")
    training_file = create_simple_training_data(train_df, nlp)

    # Train the model
    print("Training model...")
    train_model(training_file, model_dir)

    print("Training complete!")


def train_overlapping_entities_model(training_file: str, model_dir: str, test_size: float = 0.2):

    import spacy
    from spacy.training.example import Example
    from spacy.tokens import SpanGroup

    # nlp = spacy.blank("en")

    # # Prepare training data
    # span_cat = nlp.add_pipe("span_categorizer")
    # span_cat.add_label("PRODUCT")
    # span_cat.add_label("BRAND")


if __name__ == "__main__":
    input_file = r"src\training\data\Training Heartwell1 - EXC_0016 - non fieldmap .xlsx"
    model_dir = r"debug/spacy_model"
    train_ner_model(input_file, model_dir)
    # train_overlapping_entities_model(input_file, model_dir)
