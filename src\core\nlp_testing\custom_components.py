import nlp
from spacy.language import Language
from spacy.tokens import Doc
import re

@Language.component("material_entity_recognizer")
def material_entity_recognizer(doc):
    """Custom component to recognize material-related entities"""

    # Pattern for size specifications (e.g., "2 inch", "3\"", "1/2\"")
    size_pattern = re.compile(r'(\d+(?:/\d+)?(?:\s*x\s*\d+(?:/\d+)?)?)\s*(?:inch|in|\"|\')')

    # Pattern for ASTM specifications (e.g., "ASTM A106", "A53")
    astm_pattern = re.compile(r'(?:ASTM\s*)?([A-Z]\d{1,3}(?:-[A-Z0-9]+)?)')

    # Pattern for schedule specifications (e.g., "SCH 40", "Schedule 80")
    schedule_pattern = re.compile(r'(?:SCH|SCHEDULE)\s*(\d+\w*)', re.IGNORECASE)

    # Find matches and create entities
    matches = []

    # Find size specifications
    for match in size_pattern.finditer(doc.text):
        start, end = match.span(1)
        matches.append((start, end, "SIZE"))

    # Find ASTM specifications
    for match in astm_pattern.finditer(doc.text):
        start, end = match.span(1)
        matches.append((start, end, "ASTM"))

    # Find schedule specifications
    for match in schedule_pattern.finditer(doc.text):
        start, end = match.span(1)
        matches.append((start, end, "SCHEDULE"))

    # Add more patterns for other entity types

    # Create entities
    entities = []
    for start, end, label in matches:
        # Convert character offsets to token offsets
        start_token = doc.char_span(start, end)
        if start_token is not None:
            entities.append((start_token.start, start_token.end, label))

    # Set entities
    doc.ents = [nlp.tokens.Span(doc, start, end, label=label) for start, end, label in entities]

    return doc

@Language.component("material_type_classifier")
def material_type_classifier(doc):
    """Custom component to classify material types"""

    # Define material type keywords
    material_types = {
        "STEEL": ["steel", "ss", "cs", "carbon steel", "stainless steel"],
        "IRON": ["iron", "cast iron", "ductile iron"],
        "COPPER": ["copper", "brass", "bronze"],
        "PLASTIC": ["pvc", "cpvc", "hdpe", "plastic", "polyethylene"],
        "ALUMINUM": ["aluminum", "aluminium", "al"],
    }

    # Check for material type keywords in the document
    doc_text = doc.text.lower()

    for material_type, keywords in material_types.items():
        for keyword in keywords:
            if keyword in doc_text:
                # Find the span containing the keyword
                for token in doc:
                    if token.text.lower() == keyword:
                        # Create entity for the material type
                        start = token.i
                        end = token.i + 1
                        doc.ents = list(doc.ents) + [nlp.tokens.Span(doc, start, end, label="MATERIAL_TYPE")]
                        # Add a custom attribute to the doc
                        if not doc.has_extension("material_type"):
                            Doc.set_extension("material_type", default=None)
                        doc._.material_type = material_type
                        break

    return doc

def create_custom_pipeline():
    """Create a SpaCy pipeline with custom components"""
    nlp = nlp.blank("en")

    # Add custom components
    nlp.add_pipe("material_entity_recognizer")
    nlp.add_pipe("material_type_classifier")

    return nlp