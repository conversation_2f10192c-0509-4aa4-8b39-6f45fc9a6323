# SpaCy Material Description Classifier

## Overview

This project implements a custom SpaCy NLP pipeline to extract structured information from construction material descriptions. The model is designed to analyze normalized material descriptions and extract key attributes such as material type, size specifications, and component categories.

## Objectives

- Build and train a SpaCy model using an input dataset of material descriptions
- Extract correct attribute values corresponding to each description
- Normalize input descriptions for consistent processing
- Validate model predictions against known correct values
- Generate comprehensive evaluation metrics and results

## Implementation Details

### Data Processing

- Input data is loaded from Excel (.xlsx) files using pandas
- Material descriptions are normalized in the 'normalized_description' column
- The dataset is split into training and testing sets
- Valid attribute values are defined in the categorization_table

### Model Architecture

- Custom SpaCy pipeline with entity recognition components
- Text preprocessing to handle industry-specific terminology
- Entity extraction for key attributes (material, size, type, etc.)
- Post-processing to validate and normalize extracted entities

### Evaluation

- Comparison of model predictions against ground truth values
- Calculation of precision, recall, and F1 scores for each attribute
- Confusion matrix generation for categorical attributes
- Error analysis to identify common misclassification patterns

## Usage

1. Prepare your input dataset with normalized descriptions
2. Run the training script:
   ```
   python spacy_train.py --input data.xlsx --output model_output
   ```
3. Evaluate model performance:
   ```
   python spacy_evaluate.py --model model_output --test test_data.xlsx
   ```
4. Generate predictions on new data:
   ```
   python spacy_predict.py --model model_output --input new_data.xlsx
   ```

## Output

The model generates an Excel file containing:
- Original normalized descriptions
- Model predictions for each attribute with `_prediction` suffix
- Original values from the input dataset (when available)
- Confidence scores for each prediction
- Overall accuracy metrics

For example, if your input data has a column named `material`, the output will include:
- `material` - The original value from your input data
- `material_prediction` - The model's prediction for this attribute

This side-by-side format makes it easy to compare predictions with original values.

## Current Status

- [x] Data preprocessing pipeline
- [x] Basic SpaCy model configuration
- [x] Custom entity recognition components
- [x] Training pipeline implementation
- [x] Prediction with original/predicted value comparison
- [ ] Evaluation metrics calculation
- [ ] Results visualization

## Next Steps

1. Implement custom entity patterns for construction terminology
2. Add support for compound size specifications (e.g., "2 x 4")
3. Improve normalization for inconsistent descriptions
4. Add visualization of entity recognition results
5. Implement model serialization for production deployment
6. Add confidence scores for predictions

## Dependencies

- Python 3.8+
- SpaCy 3.0+
- pandas
- numpy
- scikit-learn
- openpyxl (for Excel file handling)

## References

- [SpaCy Documentation](https://spacy.io/usage/training)
- [Named Entity Recognition Guide](https://spacy.io/usage/linguistic-features#named-entities)
- [Custom Pipeline Components](https://spacy.io/usage/processing-pipelines#custom-components)

---

Keep this README file updated to track progress, adjustments to objectives and TODOs
