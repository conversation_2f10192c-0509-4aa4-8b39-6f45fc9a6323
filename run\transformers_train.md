# Multi-Label Classification Training Script

## Overview

`transformers_train.py` is a comprehensive script for training and evaluating multi-label classification models using Hugging Face Transformers. The script is specifically designed for material description classification, where each sample can have multiple labels across different categories.

## Key Features

### 🎯 **Multi-Label Classification**
- Handles multiple label categories simultaneously (e.g., material, grade, coating, etc.)
- Each label column is processed separately to avoid sparse class problems
- Supports 18 different label categories including material, ASTM standards, grades, ratings, etc.

### 🔧 **Advanced Label Encoding**
- **Column-wise processing**: Each label column (material, grade, etc.) is encoded separately
- **Format**: Labels are encoded as `{column_name}_{value}` (e.g., `material_steel`, `grade_A106`)
- **Filtering**: Only processes columns with 2-50 unique values to avoid sparse or overly complex classifications
- **Missing value handling**: Properly handles NaN, empty strings, and invalid entries

### 📊 **Threshold Optimization**
- **Individual thresholds**: Each class gets its own optimized prediction threshold
- **F1-score optimization**: Uses F1-score to find the best threshold for each class
- **Range**: Tests thresholds from 0.1 to 0.9 in 0.05 increments
- **Validation-based**: Uses the test/validation set to optimize thresholds

### 🚀 **Training Configuration**
- **Model**: BERT-base-uncased with multi-label classification head
- **Batch size**: 16 (optimized for GPU memory and gradient quality)
- **Epochs**: 10 (sufficient for convergence without overfitting)
- **Learning rate**: 2e-5 (stable learning rate for BERT fine-tuning)
- **Warmup**: 200 steps for gradual learning rate increase

## Script Structure

### Main Functions

#### `prepare_training_set(file, test_size, model_name)`
- Loads and preprocesses the Excel training data
- Performs column-wise label encoding
- Creates train/test splits
- Initializes tokenizer and model

#### `train_model(model, tokenizer, tokenized)`
- Configures training arguments
- Sets up Trainer with custom metrics
- Trains the model with evaluation

#### `optimize_thresholds(model, tokenizer, eval_dataset)`
- Finds optimal prediction thresholds for each class
- Uses F1-score as optimization metric
- Processes evaluation data in batches

#### `run_batch_inference(tokenizer, model, texts, thresholds)`
- Performs efficient batch inference
- Applies optimized thresholds if provided
- Returns predicted labels in original format

#### `analyze_predictions(predictions, output_file_prefix)`
- Analyzes prediction diversity and patterns
- Generates frequency statistics
- Saves detailed analysis to Excel

### Custom Model Class

#### `MultiLabelClassifier(nn.Module)`
- Wraps AutoModelForSequenceClassification
- Handles multi-label classification setup
- Supports class weights (currently disabled)
- Properly forwards labels for loss computation

## Input Data Format

### Required Columns
- `material_description`: Text descriptions to classify
- Label columns: Any of the 18 predefined label categories

### Label Categories
```
rfq_scope, general_category, unit_of_measure, material, 
abbreviated_material, astm, grade, rating, schedule, 
coating, forging, ends, pipe_category, valve_type, 
fitting_category, weld_category, bolt_category, gasket_category
```

### Example Input
| material_description | material | grade | astm |
|---------------------|----------|-------|------|
| "Carbon Steel Pipe A106 Grade B" | Steel | B | A106 |
| "Stainless Steel 316L Fitting" | Stainless Steel | 316L | |

## Output Format

### Prediction Excel File
The script generates an Excel file with multiple sheets:

#### 1. **Predictions Sheet**
- Original data with side-by-side actual vs predicted columns
- Format: `{label}_actual` and `{label}_pred` columns
- Example: `material_actual`, `material_pred`

#### 2. **Accuracy Summary Sheet**
- Per-label performance metrics
- Includes precision, recall, F1-score
- Exact match rates and prediction coverage

#### 3. **Overall Summary Sheet**
- Aggregate performance statistics
- Labels with >50% and >80% accuracy
- Average metrics across all labels

### Prediction Analysis
- Separate Excel file with label frequency analysis
- Shows prediction diversity statistics
- Identifies most/least common predictions

## Usage

### Basic Usage
```python
# Train and evaluate model
training_file = "path/to/training_data.xlsx"
test_file = "path/to/test_data.xlsx"
output_file = "path/to/predictions.xlsx"

run(training_file, test_file, output_file)
```

### Configuration Options
```python
# Adjust test split size
dataset, model, tokenizer, tokenized = prepare_training_set(
    file="data.xlsx", 
    test_size=0.3,  # 30% for testing
    model_name="bert-base-uncased"
)
```

## Performance Improvements

### Recent Enhancements
1. **Fixed label encoding**: Column-wise processing instead of global encoding
2. **Threshold optimization**: Individual thresholds per class
3. **Better training config**: Optimized hyperparameters
4. **Prediction analysis**: Detailed diversity metrics
5. **Improved data filtering**: Smart column selection

### Expected Benefits
- **Higher prediction diversity**: More varied and accurate predictions
- **Better class separation**: Each column treated as separate problem
- **Optimized thresholds**: Each class gets its best threshold
- **Reduced overfitting**: Better training configuration

## Dependencies

```python
torch, transformers, pandas, numpy, scikit-learn, 
datasets, openpyxl
```

## Hardware Requirements

- **GPU**: Recommended for training (CUDA support)
- **Memory**: 8GB+ RAM, 4GB+ GPU memory
- **Storage**: Space for model checkpoints (~500MB)

## Troubleshooting

### Common Issues
1. **KeyError**: Ensure all required columns exist in input data
2. **CUDA errors**: Check GPU memory and reduce batch size if needed
3. **Low diversity**: Check label distribution and threshold optimization results
4. **Poor performance**: Verify data quality and label consistency

### Debug Features
- Detailed logging throughout training and inference
- Label distribution analysis
- Prediction diversity metrics
- Sample prediction outputs for verification
