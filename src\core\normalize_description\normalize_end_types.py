import pandas as pd
import re

'''
- Stop tokenizing after first " X " combination
- Take "/" as dual values like " X " and " - "
- Remove 'No end types found tag' 
- Handle 'With Threaded Holes': "COUNTERFLANGE ISO 6162-1/SAE J518 SOCKET WELDING (SW) PN acc. STD With Threaded Holes - Coarse Pitch ISO 261 and ISO 72 CS Carbon Steel" = Incorrect: "SW X TE"
'''


# Define the end_chart as provided
end_chart = [
    ("BW", "BE"),
    ("BE", "BE"),
    ("WELDOLET", "BE"),
    ("BEVELED END", "BE"),
    ("BEVELED", "BE"),
    ("BEVELLED END", "BE"),
    ("BEVELLED", "BE"),
    ("BVLD", "BE"),
    ("BVL", "BE"),
    ("BLE", "BE"),
    ("BOE", "BE"),
    ("BSE", "BE"),
    ("WE", "BE"),
    ("BUTT WELD END", "BE"),
    ("BBE", "BE X BE"),
    ("BLD", "BL"),
    ("BLIND STUB END", "BL"),
    ("BLIND END", "BL"),
    ("FNPT", "TE"),
    ("FE", "FE"),
    ("FLGD", "FE"),
    ("FLG'D", "FE"),
    ("MFE", "FLG"),
    ("FFE", "FLG"),
    ("FPT", "FTE"),
    ("GE", "GR"),
    ("GROOVED END", "GR"),
    ("GROOVED", "GR"),
    ("MNPT", "TE"),  # corrected based on your chart
    ("MPT", "TE"),
    ("PE", "PE"),
    ("PSE", "PE"),
    ("PLE", "PE"),
    ("POE", "PE"),
    ("PLAIN END", "PE"),
    ("PLAIN", "PE"),
    ("PBE", "PE X PE"),
    ("SW", "SW"),
    ("SOCKOLET", "SW"),
    ("SOC", "SW"),
    ("SOL", "SW"),
    ("SOCKET WELD END", "SW"),
    ("TOE", "TE"),
    ("TSE", "TE"),
    ("TE", "TE"),
    ("THD", "TE"),
    ("THRD", "TE"),
    ("NPT", "TE"),
    ("THREADOLET", "TE"),
    ("THREDOLET", "TE"),
    ("FEM", "TE"),
    ("SCRD", "TE"),
    ("SCR'D", "TE"),
    ("SCREWED", "TE"),
    ("SCREW", "TE"),
    ("SE", "TE"),
    ("THR'D", "TE"),
    ("TLE", "TE"),
    ("TOL", "TE"),
    ("ME", "TE"),
    ("THREAD", "TE"),
    ("TH", "TE"),
    ("THREDED", "TE"),
    ("THREADED", "TE"),
    ("THREAD END", "TE"),
    ("TH END", "TE"),
    ("THREDED END", "TE"),
    ("THREADED END", "TE"),
    ("TF", "TE"),
    ("TBE", "TE X TE"),
    ("M&F", "TE X TE"),
    ("T&C", "TE X CE")
]

# Normalize keys for lookup
end_lookup = {}
for val, decoded in end_chart:
    norm_key = re.sub(r"[\s'\.\-]+", "", val.upper())
    end_lookup[norm_key] = decoded

def try_match_end_patterns(token):
    """
    Try to match a single token (already uppercase, punctuation removed) 
    to known end patterns or find sub-patterns within it.
    """
    # Direct match?
    if token in end_lookup:
        return [end_lookup[token]]
    
    # Try to segment it into known patterns (for cases like THREADEDPLAIN -> THREADED + PLAIN)
    # Backtracking approach:
    results = []
    def backtrack(remaining, current):
        if not remaining:
            results.append(current[:])
            return
        for i in range(1, len(remaining)+1):
            part = remaining[:i]
            if part in end_lookup:
                current.append(end_lookup[part])
                backtrack(remaining[i:], current)
                current.pop()

    backtrack(token, [])
    if results:
        # Return the first successful segmentation
        return results[0]

    # No match found
    return []

def find_end_types(description):
    """
    Extract end types from the material description and return a normalized tag or None.
    """
    # Uppercase and remove some punctuations
    desc = description.upper()
    cleaned = re.sub(r"[\,\:\;\(\)\[\]\"]", " ", desc)
    # Insert spaces around X and replace hyphens with ' X ' to treat them as separators
    cleaned = re.sub(r"X", " X ", cleaned)
    cleaned = re.sub(r"[-]", " X ", cleaned)
    # Split by whitespace
    tokens = cleaned.split()

    decoded_ends = []
    for t in tokens:
        # Normalize token
        norm_t = re.sub(r"[\s'\.\-]+", "", t)
        if not norm_t:
            continue  # Skip empty tokens
        # Attempt to match known patterns
        matches = try_match_end_patterns(norm_t)  # assume this function is defined elsewhere as before
        # If we got any matches, add them to decoded_ends
        if matches:
            decoded_ends.extend(matches)

    if not decoded_ends:
        # No recognized end patterns
        return None

    # Join all recognized end patterns with ' X '
    final_str = " X ".join(decoded_ends)
    # Normalize spacing in case multiple " X " appear
    final_str = re.sub(r"\s+X\s+", " X ", final_str.strip())

    return f"ends:{final_str}"


def create_end_tags(description):
    """
    Create tags, match types, and review messages for end types in a similar format to other tag creators.
    
    Return format: (end_tag, end_match_type, end_review)
    """
    if pd.isnull(description):
        return ("", "", "No description provided")

    end_types = find_end_types(description)
    if end_types:
        # end_types is something like "ends:BE" or "ends:TE X PE"
        # You can assign match type as desired, e.g. "ends:Exact"
        end_tag = end_types
        end_match_type = "ends:Exact"
        end_review = ""
    else:
        end_tag = ""
        end_match_type = ""
        end_review = "No end types found"

    return (end_tag, end_match_type, end_review)

# Example usage:
def main():
    # Define file paths
    input_file_path = r"unit_tests\test_files\astm tests.xlsx"
    output_file_path = r"unit_tests\test_files\output\normalized_ends.xlsx"
    
    try:
        # Read the Excel file
        df = pd.read_excel(input_file_path)
    except FileNotFoundError:
        print(f"Input file not found at {input_file_path}. Please check the path.")
        return
    except Exception as e:
        print(f"An error occurred while reading the input file: {e}")
        return

    # Check if 'material_description' column exists
    if 'material_description' not in df.columns:
        print("The input Excel file does not contain a 'material_description' column.")
        return

    # Apply the create_tags function and convert result to three columns
    output_df = df['material_description'].apply(create_end_tags)

    # Assign column names
    output_df.columns = ['Tags', 'Match Types', 'Review']

    # Concatenate the original DataFrame with the new columns
    final_df = pd.concat([df, output_df], axis=1)

    try:
        # Save the final result
        final_df.to_excel(output_file_path, index=False)
        print(f"Processed data has been saved to {output_file_path}")
    except Exception as e:
        print(f"An error occurred while saving the output file: {e}")

if __name__ == "__main__":
    main()