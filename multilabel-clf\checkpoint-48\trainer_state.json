{"best_global_step": 48, "best_metric": 0.8490006775067751, "best_model_checkpoint": "./multilabel-clf\\checkpoint-48", "epoch": 2.0, "eval_steps": 500, "global_step": 48, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4166666666666667, "grad_norm": 1.4755562543869019, "learning_rate": 2.7e-06, "loss": 1.4433, "step": 10}, {"epoch": 0.8333333333333334, "grad_norm": 1.3411080837249756, "learning_rate": 5.7000000000000005e-06, "loss": 1.4072, "step": 20}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6632444262504578, "eval_per_label_accuracy": 0.5972222222222222, "eval_runtime": 0.2774, "eval_samples_per_second": 346.11, "eval_steps_per_second": 43.264, "step": 24}, {"epoch": 1.25, "grad_norm": 2.584810495376587, "learning_rate": 8.7e-06, "loss": 1.3314, "step": 30}, {"epoch": 1.6666666666666665, "grad_norm": 1.3809592723846436, "learning_rate": 1.1700000000000001e-05, "loss": 1.2472, "step": 40}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.5573007464408875, "eval_per_label_accuracy": 0.8490006775067751, "eval_runtime": 0.2782, "eval_samples_per_second": 345.136, "eval_steps_per_second": 43.142, "step": 48}], "logging_steps": 10, "max_steps": 168, "num_input_tokens_seen": 0, "num_train_epochs": 7, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}