"""
Integration Layer for LangGraph BOM Classification

This module provides compatibility functions to interface with existing
audit_main.py infrastructure and maintain backward compatibility.
"""

import asyncio
import pandas as pd
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

# Import existing infrastructure
try:
    # Try relative import first
    from ..ai_review_audit.audit_main import (
        Langchain<PERSON>odel<PERSON><PERSON><PERSON>,
        AuditConfig,
        AuditResult,
        ModelType
    )
    EXISTING_INFRASTRUCTURE_AVAILABLE = True
    print("✓ Existing audit infrastructure loaded successfully")
except ImportError:
    try:
        # Try absolute import for direct execution
        import sys
        from pathlib import Path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir.parent))

        from ai_review_audit.audit_main import (
            Langchain<PERSON>odel<PERSON>andler,
            AuditConfig,
            AuditResult,
            ModelType
        )
        EXISTING_INFRASTRUCTURE_AVAILABLE = True
        print("✓ Existing audit infrastructure loaded successfully (absolute import)")
    except ImportError:
        # Fallback for development/testing - create mock classes
        EXISTING_INFRASTRUCTURE_AVAILABLE = False
        print("Warning: Existing audit infrastructure not available - using mock implementations")

        # Mock classes for testing
        class ModelType(Enum):
            GEMINI_20_FLASH = "gemini-2.0-flash"
            GEMINI_25_FLASH = "gemini-2.5-flash-preview-04-17"
            GEMINI_25_PRO = "gemini-2.5-pro-preview-03-25"

        @dataclass
        class AuditConfig:
            primary_model: ModelType = ModelType.GEMINI_20_FLASH
            fallback_model: ModelType = ModelType.GEMINI_25_FLASH
            max_retries: int = 3
            timeout: int = 30
            temperature: float = 0.0
            max_output_tokens: int = 2000
            api_key: Optional[str] = None

        class LangchainModelHandler:
            def __init__(self, config):
                self.config = config

        class AuditResult:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

# Handle imports for both direct execution and module import
try:
    from .state_models import (
        ClassificationState,
        WorkflowResult,
        create_initial_state
    )
except ImportError:
    # Direct execution - use absolute imports
    from state_models import (
        ClassificationState,
        WorkflowResult,
        create_initial_state
    )


@dataclass
class LangGraphStageConfig:
    """Configuration for individual workflow stages"""
    stage_name: str
    model_type: ModelType
    temperature: float = 0.0
    max_output_tokens: int = 1000
    max_retries: int = 3
    timeout: int = 30


@dataclass
class LangGraphConfig:
    """Enhanced configuration for LangGraph workflow with per-stage model selection"""

    # Stage-specific model configurations
    material_analysis_config: LangGraphStageConfig = None
    fitting_classification_config: LangGraphStageConfig = None
    pipe_classification_config: LangGraphStageConfig = None
    qa_decisions_config: LangGraphStageConfig = None
    self_audit_config: LangGraphStageConfig = None

    # Global settings
    debug_mode: bool = False
    max_concurrent: int = 10
    api_key: Optional[str] = None

    def __post_init__(self):
        """Set default configurations for stages if not provided"""
        if self.material_analysis_config is None:
            self.material_analysis_config = LangGraphStageConfig(
                stage_name="material_analysis",
                model_type=ModelType.GEMINI_20_FLASH,
                temperature=0.0,
                max_output_tokens=800,
                max_retries=3
            )

        if self.fitting_classification_config is None:
            self.fitting_classification_config = LangGraphStageConfig(
                stage_name="fitting_classification",
                model_type=ModelType.GEMINI_25_FLASH,
                temperature=0.0,
                max_output_tokens=1200,
                max_retries=3
            )

        if self.pipe_classification_config is None:
            self.pipe_classification_config = LangGraphStageConfig(
                stage_name="pipe_classification",
                model_type=ModelType.GEMINI_20_FLASH,
                temperature=0.0,
                max_output_tokens=1000,
                max_retries=3
            )

        if self.qa_decisions_config is None:
            self.qa_decisions_config = LangGraphStageConfig(
                stage_name="qa_decisions",
                model_type=ModelType.GEMINI_25_FLASH,
                temperature=0.0,
                max_output_tokens=800,
                max_retries=2
            )

        if self.self_audit_config is None:
            self.self_audit_config = LangGraphStageConfig(
                stage_name="self_audit",
                model_type=ModelType.GEMINI_25_FLASH,
                temperature=0.0,
                max_output_tokens=1000,
                max_retries=2
            )


class LangGraphModelHandler:
    """
    Enhanced model handler for LangGraph workflow with flexible per-stage model configuration
    """

    def __init__(self, config: Optional[LangGraphConfig] = None):
        self.config = config or LangGraphConfig()
        self.langchain_handlers = {}

        if EXISTING_INFRASTRUCTURE_AVAILABLE:
            self._setup_model_handlers()
        else:
            error_msg = "❌ LangGraphModelHandler: Existing audit infrastructure not available. Cannot proceed without actual model integration."
            if self.config.debug_mode:
                print(error_msg)
            raise RuntimeError(error_msg)

    def _setup_model_handlers(self):
        """Initialize direct Langchain model handlers for each stage"""
        try:
            # Import Langchain directly for custom model handling
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.messages import SystemMessage, HumanMessage
            import os

            # Configure API key
            api_key = self.config.api_key or os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY or GEMINI_API_KEY not found in environment variables or config")

            # Set the environment variable for Langchain
            os.environ["GOOGLE_API_KEY"] = api_key

            # Create unique model types needed
            unique_models = set()
            stage_configs = [
                self.config.material_analysis_config,
                self.config.fitting_classification_config,
                self.config.pipe_classification_config,
                self.config.qa_decisions_config,
                self.config.self_audit_config
            ]

            for stage_config in stage_configs:
                unique_models.add(stage_config.model_type)

            # Create direct Langchain handlers for each unique model
            for model_type in unique_models:
                try:
                    # Create base model with enhanced retry settings
                    base_model = ChatGoogleGenerativeAI(
                        model=model_type.value,
                        temperature=0.0,  # Always deterministic
                        max_tokens=2000,
                        max_retries=3,
                        timeout=60
                    )

                    self.langchain_handlers[model_type] = base_model

                    if self.config.debug_mode:
                        print(f"✓ Initialized direct Langchain model: {model_type.value}")

                except Exception as e:
                    print(f"❌ Failed to initialize {model_type.value}: {e}")

            if self.config.debug_mode:
                print(f"✓ LangGraphModelHandler: Initialized {len(self.langchain_handlers)} direct model handlers")

        except Exception as e:
            print(f"❌ Error setting up model handlers: {e}")
            self.langchain_handlers = None

    async def call_model_for_stage(
        self,
        stage_name: str,
        prompt: str,
        response_schema: Any,
        item_id: str = "unknown"
    ) -> Any:
        """
        Call model for specific workflow stage with stage-specific configuration

        Args:
            stage_name: Name of the workflow stage
            prompt: The prompt to send to the model
            response_schema: Pydantic model for structured response
            item_id: Item ID for logging/debugging

        Returns:
            Structured response parsed according to response_schema
        """

        # Get stage configuration
        stage_config = self._get_stage_config(stage_name)
        if not stage_config:
            raise ValueError(f"No configuration found for stage: {stage_name}")

        # Check if model handlers are not available
        if not self.langchain_handlers:
            error_msg = f"❌ {stage_name}: No model handlers available. Cannot proceed without actual model integration."
            if self.config.debug_mode:
                print(error_msg)
            raise RuntimeError(error_msg)

        # Get appropriate model handler
        model_handler = self.langchain_handlers.get(stage_config.model_type)
        if not model_handler:
            raise RuntimeError(f"No model handler available for {stage_config.model_type}")

        try:
            if self.config.debug_mode:
                print(f"🤖 {stage_name}: Calling {stage_config.model_type.value} (temp: {stage_config.temperature})")

            # Import message types for direct Langchain calls
            from langchain_core.messages import SystemMessage, HumanMessage

            # Create messages for the model call
            # For LangGraph, we send the prompt directly without a system message
            # since each stage has its own specific prompt
            messages = [
                HumanMessage(content=prompt)
            ]

            # Call the model directly
            response = await model_handler.ainvoke(messages)

            # Extract content from Langchain response
            if hasattr(response, 'content'):
                response_content = response.content
            else:
                response_content = str(response)

            if self.config.debug_mode:
                print(f"🔍 {stage_name}: Raw model response: {response_content[:200]}...")

            # Try to parse as JSON - handle markdown code blocks
            try:
                import json
                import re

                # Extract JSON from markdown code blocks if present
                json_content = response_content.strip()

                # Check for markdown code blocks
                if json_content.startswith('```json') and json_content.endswith('```'):
                    # Extract JSON from markdown code block
                    json_content = json_content[7:-3].strip()  # Remove ```json and ```
                elif json_content.startswith('```') and json_content.endswith('```'):
                    # Extract from generic code block
                    json_content = json_content[3:-3].strip()  # Remove ``` and ```

                # Try to parse the JSON - improved logic
                json_content = json_content.strip()

                # Attempt JSON parsing directly
                try:
                    parsed_response = json.loads(json_content)

                    if self.config.debug_mode:
                        print(f"✅ {stage_name}: Successfully parsed JSON response")

                    # Try to create the response schema from the parsed JSON
                    if response_schema != dict:
                        try:
                            return response_schema(**parsed_response)
                        except Exception as parse_error:
                            if self.config.debug_mode:
                                print(f"⚠️  {stage_name}: Schema parsing failed, returning raw dict: {parse_error}")
                            return parsed_response
                    else:
                        return parsed_response

                except json.JSONDecodeError:
                    # If direct parsing fails, check if it looks like JSON but has issues
                    if (json_content.startswith('{') and json_content.endswith('}')) or \
                       (json_content.startswith('[') and json_content.endswith(']')):
                        # Try to clean up common JSON issues
                        cleaned_content = json_content.replace('\n', ' ').replace('\r', '')
                        try:
                            parsed_response = json.loads(cleaned_content)

                            if self.config.debug_mode:
                                print(f"✅ {stage_name}: Successfully parsed cleaned JSON response")

                            if response_schema != dict:
                                try:
                                    return response_schema(**parsed_response)
                                except Exception as parse_error:
                                    if self.config.debug_mode:
                                        print(f"⚠️  {stage_name}: Schema parsing failed, returning raw dict: {parse_error}")
                                    return parsed_response
                            else:
                                return parsed_response

                        except json.JSONDecodeError:
                            pass  # Fall through to text wrapping

                    # Not valid JSON, return as text in a dict
                    if self.config.debug_mode:
                        print(f"⚠️  {stage_name}: Response is not valid JSON format, wrapping in dict")
                    return {"response": response_content}

            except json.JSONDecodeError as e:
                # Not valid JSON, return as text in a dict
                if self.config.debug_mode:
                    print(f"⚠️  {stage_name}: JSON decode error: {e}, wrapping in dict")
                return {"response": response_content}

        except Exception as e:
            if self.config.debug_mode:
                print(f"❌ {stage_name}: Model call failed: {e}")

            # No fallback to simulation - raise the error
            raise RuntimeError(f"❌ {stage_name}: Model call failed: {e}. No simulation fallback available.")

    def _get_stage_config(self, stage_name: str) -> Optional[LangGraphStageConfig]:
        """Get configuration for specific stage"""
        stage_config_map = {
            "material_analysis": self.config.material_analysis_config,
            "fitting_classification": self.config.fitting_classification_config,
            "pipe_classification": self.config.pipe_classification_config,
            "qa_decisions": self.config.qa_decisions_config,
            "self_audit": self.config.self_audit_config
        }
        return stage_config_map.get(stage_name)




def convert_to_audit_result(workflow_result: WorkflowResult) -> AuditResult:
    """
    Convert LangGraph WorkflowResult to existing AuditResult format

    This ensures compatibility with existing correction application workflow.
    """

    if not EXISTING_INFRASTRUCTURE_AVAILABLE:
        # Return mock result for testing
        return {
            "item_id": workflow_result.item_id,
            "status": workflow_result.status,
            "model_used": "langgraph_gemini",
            "processing_time": workflow_result.processing_time,
            "error_message": workflow_result.error_message
        }

    # Convert to actual AuditResult when infrastructure is available
    return AuditResult(
        item_id=workflow_result.item_id,
        status=workflow_result.status,
        model_used=workflow_result.model_used or "langgraph_gemini",
        processing_time=workflow_result.processing_time,
        error_message=workflow_result.error_message,
        # Add other required fields as needed
    )


def convert_to_programmatic_format(
    workflow_results: List[WorkflowResult],
    original_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Convert LangGraph results to existing programmatic format

    This maintains compatibility with the existing correction application workflow
    that expects a specific DataFrame structure.
    """

    programmatic_data = []

    for result in workflow_results:
        if result.identified_issues:
            for issue in result.identified_issues:
                programmatic_data.append({
                    'id': result.item_id,
                    'status': "issues",
                    'material_description': original_df[
                        original_df['id'] == result.item_id
                    ]['material_description'].iloc[0] if not original_df.empty else "Unknown",
                    'column_name': issue["field"],
                    'current_value': issue["current_value"],
                    'suggested_value': issue["suggested_value"],
                    'confidence': issue["confidence"],
                    'explanation': issue["explanation"],
                    'accept_merge': None  # User decision column
                })
        else:
            # No issues found
            programmatic_data.append({
                'id': result.item_id,
                'status': "ok",
                'material_description': original_df[
                    original_df['id'] == result.item_id
                ]['material_description'].iloc[0] if not original_df.empty else "Unknown",
                'column_name': None,
                'current_value': None,
                'suggested_value': None,
                'confidence': None,
                'explanation': "No issues found",
                'accept_merge': None
            })

    return pd.DataFrame(programmatic_data)


async def audit_bom_dataframe_langgraph(
    df: pd.DataFrame,
    config: Optional[Any] = None,
    max_concurrent: int = 10,
    debug_mode: bool = False
) -> List[WorkflowResult]:
    """
    LangGraph-based audit maintaining existing interface

    This function provides the same interface as the existing audit_bom_dataframe
    but uses the new LangGraph workflow internally.
    """

    # Import the main workflow (will be implemented in langgraph_main.py)
    try:
        from .langgraph_main import create_classification_workflow
    except ImportError:
        from langgraph_main import create_classification_workflow

    # Create LangGraph workflow
    workflow = create_classification_workflow(config)

    # Process items with existing concurrency control
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_item_with_semaphore(item):
        async with semaphore:
            # Create initial state
            state = create_initial_state(
                item_id=str(item.get("id", "unknown")),
                material_description=item.get("material_description", ""),
                original_classification=item,
                debug_mode=debug_mode
            )

            # Run workflow
            final_state = await workflow.ainvoke(state)

            # Convert to WorkflowResult
            return WorkflowResult(
                item_id=final_state["item_id"],
                status="issues" if final_state.get("identified_issues") else "ok",
                final_classifications=final_state.get("field_classifications", {}),
                confidence_scores=final_state.get("confidence_scores", {}),
                identified_issues=final_state.get("identified_issues", []),
                suggested_corrections=final_state.get("suggested_corrections", {}),
                workflow_path=final_state.get("workflow_path", []),
                processing_time=final_state.get("processing_time", 0.0),
                model_calls=final_state.get("model_calls", 0),
                tokens_used=final_state.get("tokens_used", 0),
                stage_outputs=final_state.get("stage_outputs") if debug_mode else None,
                model_used="langgraph_gemini"
            )

    # Prepare items for processing
    items = df.to_dict('records')

    # Process all items concurrently
    tasks = [process_item_with_semaphore(item) for item in items]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions
    workflow_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            # Create error result
            workflow_results.append(WorkflowResult(
                item_id=str(items[i].get("id", f"error_{i}")),
                status="error",
                error_message=str(result),
                model_used="langgraph_gemini"
            ))
        else:
            workflow_results.append(result)

    return workflow_results


def get_model_handler(config: Optional[LangGraphConfig] = None) -> LangGraphModelHandler:
    """Get model handler for LangGraph workflow"""
    return LangGraphModelHandler(config)


def create_default_langgraph_config(
    debug_mode: bool = False,
    api_key: Optional[str] = None
) -> LangGraphConfig:
    """Create default LangGraph configuration with optimized model assignments"""

    return LangGraphConfig(
        # Stage 1: Material Analysis - Fast model for quick categorization
        material_analysis_config=LangGraphStageConfig(
            stage_name="material_analysis",
            model_type=ModelType.GEMINI_20_FLASH,
            temperature=0.0,
            max_output_tokens=800,
            max_retries=3
        ),

        # Stage 2: Category Classification - More powerful model for complex classification
        fitting_classification_config=LangGraphStageConfig(
            stage_name="fitting_classification",
            model_type=ModelType.GEMINI_25_FLASH,
            temperature=0.0,
            max_output_tokens=1200,
            max_retries=3
        ),

        pipe_classification_config=LangGraphStageConfig(
            stage_name="pipe_classification",
            model_type=ModelType.GEMINI_20_FLASH,
            temperature=0.0,
            max_output_tokens=1000,
            max_retries=3
        ),

        # Stage 3: Q&A Decisions - Powerful model for complex reasoning
        qa_decisions_config=LangGraphStageConfig(
            stage_name="qa_decisions",
            model_type=ModelType.GEMINI_25_FLASH,
            temperature=0.0,
            max_output_tokens=800,
            max_retries=2
        ),

        # Stage 4: Self-Audit - Powerful model for validation
        self_audit_config=LangGraphStageConfig(
            stage_name="self_audit",
            model_type=ModelType.GEMINI_25_FLASH,
            temperature=0.0,
            max_output_tokens=1000,
            max_retries=2
        ),

        # Global settings
        debug_mode=debug_mode,
        max_concurrent=10,
        api_key=api_key
    )


def create_stage1_only_config(
    model_type: ModelType = ModelType.GEMINI_20_FLASH,
    debug_mode: bool = True,
    api_key: Optional[str] = None
) -> LangGraphConfig:
    """Create configuration for testing Stage 1 only"""

    config = create_default_langgraph_config(
        debug_mode=debug_mode,
        api_key=api_key
    )

    # Override material analysis config with specified model
    config.material_analysis_config = LangGraphStageConfig(
        stage_name="material_analysis",
        model_type=model_type,
        temperature=0.0,
        max_output_tokens=800,
        max_retries=3,
        timeout=30
    )

    return config


# Migration utilities
def compare_results_with_monolithic(
    langgraph_results: List[WorkflowResult],
    monolithic_results: List[Any],
    comparison_fields: List[str] = None
) -> Dict[str, Any]:
    """
    Compare LangGraph results with monolithic system results

    This utility helps with A/B testing and migration validation.
    """

    if comparison_fields is None:
        comparison_fields = ["rfq_scope", "material", "astm", "fitting_category"]

    comparison_stats = {
        "total_items": len(langgraph_results),
        "agreement_rate": 0.0,
        "field_agreement": {},
        "processing_time_comparison": {},
        "accuracy_improvements": [],
        "discrepancies": []
    }

    # TODO: Implement detailed comparison logic
    # This would compare field-by-field accuracy and identify improvements

    return comparison_stats


if __name__ == "__main__":
    """Test integration layer functions"""

    print("Testing LangGraph Integration Layer")
    print("=" * 50)

    # Test data
    test_df = pd.DataFrame([
        {
            "id": "test_001",
            "material_description": "Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            "rfq_scope": "Pipe",  # Incorrect - should be Fittings
            "material": "Steel, Carbon"
        },
        {
            "id": "test_002",
            "material_description": "90 LR Elbow 4\" ASTM A234 WPB",
            "rfq_scope": "Fittings",
            "material": "Steel, Carbon"
        }
    ])

    print("✓ Test DataFrame created")
    print(f"  Items: {len(test_df)}")
    print(f"  Columns: {list(test_df.columns)}")

    # Test WorkflowResult creation
    test_workflow_result = WorkflowResult(
        item_id="test_001",
        status="issues",
        final_classifications={
            "rfq_scope": "Fittings",
            "fitting_category": "Nipple",
            "material": "Steel, Carbon"
        },
        confidence_scores={
            "rfq_scope": 0.95,
            "fitting_category": 0.90,
            "material": 0.85
        },
        identified_issues=[
            {
                "field": "rfq_scope",
                "current_value": "Pipe",
                "suggested_value": "Fittings",
                "confidence": 0.95,
                "explanation": "Pipe Nipple should be classified as Fitting, not Pipe"
            }
        ],
        workflow_path=["material_analysis", "fitting_classification", "qa_decisions", "self_audit"],
        processing_time=2.1,
        model_calls=4,
        tokens_used=850
    )

    print("\n✓ WorkflowResult created")
    print(f"  Status: {test_workflow_result.status}")
    print(f"  Issues: {len(test_workflow_result.identified_issues)}")
    print(f"  Processing time: {test_workflow_result.processing_time}s")

    # Test conversion to programmatic format
    programmatic_df = convert_to_programmatic_format([test_workflow_result], test_df)

    print("\n✓ Programmatic format conversion")
    print(f"  Rows: {len(programmatic_df)}")
    print(f"  Columns: {list(programmatic_df.columns)}")
    if not programmatic_df.empty:
        print(f"  Sample issue: {programmatic_df.iloc[0]['explanation']}")

    # Test model handler
    model_handler = get_model_handler()
    print(f"\n✓ Model handler created")
    print(f"  Type: {type(model_handler).__name__}")
    print(f"  Infrastructure available: {EXISTING_INFRASTRUCTURE_AVAILABLE}")

    print("\n" + "=" * 50)
    print("Integration layer tested successfully!")
    print("Ready for LangGraph main workflow implementation.")
