{"best_global_step": 15, "best_metric": 0.491260162601626, "best_model_checkpoint": "./multilabel-clf\\checkpoint-15", "epoch": 1.0, "eval_steps": 500, "global_step": 15, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.6666666666666666, "grad_norm": 1.441255807876587, "learning_rate": 2.7e-06, "loss": 1.4422, "step": 10}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.7007958292961121, "eval_per_label_accuracy": 0.491260162601626, "eval_runtime": 0.6942, "eval_samples_per_second": 345.703, "eval_steps_per_second": 43.213, "step": 15}], "logging_steps": 10, "max_steps": 105, "num_input_tokens_seen": 0, "num_train_epochs": 7, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}