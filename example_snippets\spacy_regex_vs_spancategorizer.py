import spacy
from spacy.pipeline import SpanCategorizer
from spacy.tokens import Span
from spacy.training.example import Example

nlp = spacy.blank("en")

# Add SpanCategorizer manually
config = {"spans_key": "sc"}
span_cat = nlp.add_pipe("spancat", config=config)
span_cat.add_label("ORG")

texts = [
    "Apple announced a new product.",
    "The apple fell from the tree.",
    "Appl is a startup.",
    "The application is ready."
]

# Training data
TRAIN_DATA = [
    ("Apple announced a new product.", [(0, 5, "ORG")]),  # Apple company
    ("The apple fell from the tree.", []),               # apple fruit, no label
    ("Appl is a startup.", [(0, 4, "ORG")]),             # Appl company typo
    ("The application is ready.", [])                     # no entity
]

# Prepare training examples
examples = []
for text, spans in TRAIN_DATA:
    doc = nlp.make_doc(text)
    entities = []
    for start, end, label in spans:
        span = doc.char_span(start, end, label=label)
        if span:
            entities.append(span)
    doc.spans["candidates"] = entities
    example = Example.from_dict(doc, {"spans": {"candidates": entities}})
    examples.append(example)

# Initialize and train
nlp.initialize(lambda: examples)
for _ in range(15):
    nlp.update(examples)

# Prediction on new text with fuzzy matches
test_text = "Apple apple appl applic"
doc = nlp(test_text)
# Generate candidate spans with fuzzy or regex (simulate here)
candidates = [
    doc.char_span(0, 5),   # Apple
    doc.char_span(6, 11),  # apple
    doc.char_span(12, 16), # appl
    doc.char_span(17, 24), # applic (application truncated)
]
doc.spans["candidates"] = [span for span in candidates if span is not None]

# Predict labels for candidates
docs = list(nlp.pipe([doc.text]))
for span in docs[0].spans["candidates"]:
    pred = span_cat.predict_span(docs[0], span)
    print(f"Span '{span.text}' classified as: {pred}")