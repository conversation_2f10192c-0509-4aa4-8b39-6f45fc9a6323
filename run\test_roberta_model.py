from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onfig, RobertaPreTrainedModel, RobertaModel
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import os
import re
import pickle
import argparse
from sklearn.metrics import classification_report, f1_score, precision_score, recall_score
from torch.utils.data import Dataset

USE_CUDA = True
DEVICE = "cuda" if USE_CUDA and torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

labels_to_classify = [
    "rfq_scope",
    "general_category",
    # "unit_of_measure",
    # "material",
    # "abbreviated_material",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    # "forging",
    "ends",
    # "pipe_category",
    # "valve_type",
    # "fitting_category",
    # "weld_category",
    # "bolt_category",
    # "gasket_category"
]

# Load data from Excel file
def load_data(file_path):
    print(f"Loading data from {file_path}")
    df = pd.read_excel(file_path)
    print(f"Loaded {len(df)} rows of data")
    return df

def normalize_punctuation(text):
    # Replace multiple commas with a single comma
    text = re.sub(r',{2,}', ',', text)

    # Remove punctuation except for commas and decimal points in numbers
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):
            # This part contains a number, so preserve the decimal point
            cleaned_part = re.sub(r'[;:!?]', '', part)
        else:
            # This part doesn't contain a number, so remove all punctuation except commas
            cleaned_part = re.sub(r'[.;:!?]', '', part)
        cleaned_parts.append(cleaned_part)

    return ','.join(cleaned_parts)

def remove_extra_spaces(text):
    # Replace multiple spaces with a single space, but keep single spaces and spaces in measurements
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):  # This part contains a number (likely a measurement)
            cleaned_parts.append(part.strip())
        else:
            cleaned_parts.append(re.sub(r' {2,}', ' ', part.strip()))
    return ','.join(cleaned_parts)

def normalize_description(description):
    if pd.isna(description):
        return ""

    normalized = str(description) # Ensure string
    normalized = remove_extra_spaces(normalized) # Fix spacing
    normalized = normalize_punctuation(normalized) # Normalize punctuation
    normalized = normalized.upper()
    return normalized

# Prepare data for testing
def prepare_data(df, mlb):
    # Normalize descriptions
    df['normalized_description'] = df['material_description'].apply(normalize_description)

    # Extract texts
    texts = df['normalized_description'].tolist()

    # Extract labels
    labels = []
    for _, row in df.iterrows():
        row_labels = []
        for label in labels_to_classify:
            if pd.notna(row[label]):
                row_labels.append(f"{label}_{row[label]}")
        labels.append(row_labels)

    # Convert labels to multi-hot vectors using the provided mlb
    y = mlb.transform(labels)
    label_names = mlb.classes_

    return texts, y, label_names

class MultiLabelDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len=128):
        self.encodings = tokenizer(texts, truncation=True, padding=True, max_length=max_len)
        self.labels = torch.tensor(labels, dtype=torch.float32)

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item["labels"] = self.labels[idx]
        return item

class RobertaForMultiLabel(RobertaPreTrainedModel):
    def __init__(self, config, num_labels):
        super().__init__(config)
        self.roberta = RobertaModel(config)
        self.dropout = nn.Dropout(0.3)
        self.classifier = nn.Linear(config.hidden_size, num_labels)
        self.loss_fn = nn.BCEWithLogitsLoss()

    def forward(self, input_ids=None, attention_mask=None, labels=None):
        outputs = self.roberta(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = self.dropout(outputs.pooler_output)
        logits = self.classifier(pooled_output)

        loss = None
        if labels is not None:
            loss = self.loss_fn(logits, labels)

        from transformers.modeling_outputs import SequenceClassifierOutput
        return SequenceClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions
        )

def load_model(model_path):
    """Load the trained model from the specified path"""
    print(f"Loading model from {model_path}")

    # Load label binarizer first
    with open(os.path.join(model_path, "label_binarizer.pkl"), "rb") as f:
        mlb = pickle.load(f)

    print(f"Loaded label binarizer with {len(mlb.classes_)} classes: {mlb.classes_}")

    # Load configuration
    config = RobertaConfig.from_pretrained(model_path)

    # Get the number of labels from the saved model config
    num_labels = len(mlb.classes_)
    print(f"Using {num_labels} labels from the saved model")

    # Initialize model with the config and number of labels from mlb
    model = RobertaForMultiLabel(config, num_labels=num_labels)

    # Load the state dict with strict=False to allow for mismatches
    state_dict = torch.load(os.path.join(model_path, "pytorch_model.bin"), map_location=DEVICE)

    # Filter out mismatched classifier weights
    filtered_state_dict = {k: v for k, v in state_dict.items()
                          if not (('classifier' in k) and (v.shape != model.state_dict()[k].shape))}

    # Load the filtered state dict
    missing_keys, unexpected_keys = model.load_state_dict(filtered_state_dict, strict=False)
    print(f"Missing keys: {missing_keys}")
    print(f"Unexpected keys: {unexpected_keys}")

    model.to(DEVICE)

    # Load tokenizer
    tokenizer = RobertaTokenizer.from_pretrained(model_path)

    return model, tokenizer, mlb

def evaluate_model(model, test_dataset, label_names):
    """Evaluate the model on the test dataset"""
    print("Evaluating model on test dataset...")

    # Get predictions
    all_preds = []
    all_labels = []

    for i in range(len(test_dataset)):
        # Get input
        inputs = {k: v.unsqueeze(0).to(DEVICE) for k, v in test_dataset[i].items() if k != "labels"}
        labels = test_dataset[i]["labels"].cpu().numpy()

        # Get predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probs = torch.sigmoid(logits).cpu().numpy()[0]
            preds = (probs > 0.5).astype(int)

        all_preds.append(preds)
        all_labels.append(labels)

    # Convert to numpy arrays
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # Calculate metrics
    # Overall metrics
    accuracy = np.mean(np.all(all_preds == all_labels, axis=1))
    hamming_loss = np.mean(np.mean(all_preds != all_labels, axis=1))

    # Per-class metrics
    precision = precision_score(all_labels, all_preds, average=None, zero_division=0)
    recall = recall_score(all_labels, all_preds, average=None, zero_division=0)
    f1 = f1_score(all_labels, all_preds, average=None, zero_division=0)

    # Macro-averaged metrics
    macro_precision = precision_score(all_labels, all_preds, average='macro', zero_division=0)
    macro_recall = recall_score(all_labels, all_preds, average='macro', zero_division=0)
    macro_f1 = f1_score(all_labels, all_preds, average='macro', zero_division=0)

    # Micro-averaged metrics
    micro_precision = precision_score(all_labels, all_preds, average='micro', zero_division=0)
    micro_recall = recall_score(all_labels, all_preds, average='micro', zero_division=0)
    micro_f1 = f1_score(all_labels, all_preds, average='micro', zero_division=0)

    # Weighted-averaged metrics
    weighted_precision = precision_score(all_labels, all_preds, average='weighted', zero_division=0)
    weighted_recall = recall_score(all_labels, all_preds, average='weighted', zero_division=0)
    weighted_f1 = f1_score(all_labels, all_preds, average='weighted', zero_division=0)

    # Print results
    print("\n===== Model Performance Metrics =====")
    print(f"Exact Match Accuracy: {accuracy:.4f}")
    print(f"Hamming Loss: {hamming_loss:.4f}")
    print("\nMacro-averaged Metrics:")
    print(f"  Precision: {macro_precision:.4f}")
    print(f"  Recall: {macro_recall:.4f}")
    print(f"  F1 Score: {macro_f1:.4f}")
    print("\nMicro-averaged Metrics:")
    print(f"  Precision: {micro_precision:.4f}")
    print(f"  Recall: {micro_recall:.4f}")
    print(f"  F1 Score: {micro_f1:.4f}")
    print("\nWeighted-averaged Metrics:")
    print(f"  Precision: {weighted_precision:.4f}")
    print(f"  Recall: {weighted_recall:.4f}")
    print(f"  F1 Score: {weighted_f1:.4f}")

    # Per-class metrics
    print("\nPer-class Metrics:")
    print(f"{'Label':<30} {'Precision':<10} {'Recall':<10} {'F1 Score':<10} {'Support':<10}")
    print("-" * 70)
    for i, label in enumerate(label_names):
        support = np.sum(all_labels[:, i])
        print(f"{label:<30} {precision[i]:<10.4f} {recall[i]:<10.4f} {f1[i]:<10.4f} {support:<10}")

    # Print classification report
    print("\nDetailed Classification Report:")
    report = classification_report(all_labels, all_preds, target_names=label_names, zero_division=0)
    print(report)

    # Calculate and store metrics for return
    metrics = {
        'accuracy': accuracy,
        'hamming_loss': hamming_loss,
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'micro_precision': micro_precision,
        'micro_recall': micro_recall,
        'micro_f1': micro_f1,
        'weighted_precision': weighted_precision,
        'weighted_recall': weighted_recall,
        'weighted_f1': weighted_f1,
        'per_class_precision': precision,
        'per_class_recall': recall,
        'per_class_f1': f1
    }

    return all_preds, all_labels, metrics

def test_samples(model, tokenizer, texts, label_names, true_labels=None):
    """Test the model on individual text samples"""
    print("\nTesting model on individual samples:")

    for i, text in enumerate(texts):
        # Normalize text
        text_normalized = normalize_description(text)

        # Tokenize the input
        inputs = tokenizer(text_normalized, return_tensors="pt", truncation=True, padding=True)
        inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

        # Get predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probs = torch.sigmoid(logits).cpu().numpy()[0]
            preds = (probs > 0.5).astype(int)

        # Print results
        print(f"\nSample {i+1}:")
        print(f"Input: {text}")
        print(f"Normalized: {text_normalized}")

        print("Predicted labels:")
        for j, (label, prob, pred) in enumerate(zip(label_names, probs, preds)):
            if pred == 1:
                print(f"  {label}: {prob:.4f}")

        # Print true labels if available
        if true_labels is not None:
            print("True labels:")
            for j, (label, is_true) in enumerate(zip(label_names, true_labels[i])):
                if is_true == 1:
                    print(f"  {label}")

def main():
    model_path = r"models\roberta-material-classifier\final_model"
    num_samples = 5
    test_file = r"src\training\data\Training Heartwell1 - EXC_0016 - non fieldmap .xlsx"
    # test_file = r"src\training\data\rfq_template-example.xlsx"

    # Load model, tokenizer, and label binarizer
    model, tokenizer, mlb = load_model(model_path)

    # Load and prepare test data
    test_df = load_data(test_file)
    test_texts, test_labels, label_names = prepare_data(test_df, mlb)

    # Store original descriptions for results
    original_descriptions = test_df['material_description'].tolist()

    # Create test dataset
    test_dataset = MultiLabelDataset(test_texts, test_labels, tokenizer)

    # Evaluate model on test dataset
    all_preds, all_labels, metrics = evaluate_model(model, test_dataset, label_names)

    # Test on individual samples
    num_samples = min(num_samples, len(test_texts))
    sample_texts = test_texts[:num_samples]
    sample_labels = test_labels[:num_samples]
    test_samples(model, tokenizer, sample_texts, label_names, sample_labels)

    # Save results to Excel
    results_df = pd.DataFrame({
        'original_text': original_descriptions,
        'normalized_text': test_texts
    })

    # Add true and predicted labels
    for i, label in enumerate(label_names):
        results_df[f'true_{label}'] = all_labels[:, i]
        results_df[f'pred_{label}'] = all_preds[:, i]

    # Extract just the filename without path
    test_filename = os.path.basename(test_file)

    # Add test filename as the last column
    results_df['source_file'] = test_filename

    # Add metrics to a separate sheet
    metrics_df = pd.DataFrame({
        'Metric': [
            'Model Path',
            'Test File',
            'Exact Match Accuracy', 'Hamming Loss',
            'Macro Precision', 'Macro Recall', 'Macro F1',
            'Micro Precision', 'Micro Recall', 'Micro F1',
            'Weighted Precision', 'Weighted Recall', 'Weighted F1'
        ],
        'Value': [
            model_path,
            test_file,
            metrics['accuracy'], metrics['hamming_loss'],
            metrics['macro_precision'], metrics['macro_recall'], metrics['macro_f1'],
            metrics['micro_precision'], metrics['micro_recall'], metrics['micro_f1'],
            metrics['weighted_precision'], metrics['weighted_recall'], metrics['weighted_f1']
        ]
    })

    # Add per-class metrics
    per_class_metrics = []
    for i, label in enumerate(label_names):
        per_class_metrics.append({
            'Label': label,
            'Precision': metrics['per_class_precision'][i],
            'Recall': metrics['per_class_recall'][i],
            'F1': metrics['per_class_f1'][i],
            'Support': np.sum(all_labels[:, i])
        })
    per_class_df = pd.DataFrame(per_class_metrics)

    # Save to Excel with multiple sheets
    results_path = os.path.join(os.path.dirname(model_path), "test_results.xlsx")
    with pd.ExcelWriter(results_path, engine='openpyxl') as writer:
        results_df.to_excel(writer, sheet_name='Predictions', index=False)
        metrics_df.to_excel(writer, sheet_name='Overall Metrics', index=False)
        per_class_df.to_excel(writer, sheet_name='Per-class Metrics', index=False)

        # Apply formatting to each worksheet
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]

            # Freeze the first row and column
            worksheet.freeze_panes = 'B2'

            # Add auto-filter to the header row
            worksheet.auto_filter.ref = worksheet.dimensions

            # Auto-adjust column widths based on content
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                # Find the maximum length in the column
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                # Set the column width based on the maximum length (with some padding)
                adjusted_width = (max_length + 2)
                worksheet.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Cap width at 50

    print(f"\nTest results saved to: {results_path}")

if __name__ == "__main__":
    main()
