from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RobertaPreTrainedModel
from transformers import TrainingArguments, Trainer
from sklearn.preprocessing import MultiLabelBinarizer
from torch.utils.data import Dataset
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import os
from sklearn.model_selection import train_test_split
import re

USE_CUDA = True
DEVICE = "cuda" if USE_CUDA and torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

labels_to_train = [
    "rfq_scope",
    "general_category",
    # "unit_of_measure",
    # "material",
    # "abbreviated_material",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    # "forging",
    "ends",
    # "pipe_category",
    # "valve_type",
    # "fitting_category",
    # "weld_category",
    # "bolt_category",
    # "gasket_category"
]

# Load data from Excel file
def load_data(file_path):
    print(f"Loading data from {file_path}")
    df = pd.read_excel(file_path)

    # Check if required columns exist
    required_cols = ['material_description', 'astm', 'grade']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"Required column '{col}' not found in the Excel file")

    # Filter out rows with missing values in key columns
    df = df.dropna(subset=['material_description'])
    print(f"Loaded {len(df)} rows of data")

    return df

def normalize_punctuation(text):
    # Replace multiple commas with a single comma
    text = re.sub(r',{2,}', ',', text)

    # Remove punctuation except for commas and decimal points in numbers
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):
            # This part contains a number, so preserve the decimal point
            cleaned_part = re.sub(r'[;:!?]', '', part)
        else:
            # This part doesn't contain a number, so remove all punctuation except commas
            cleaned_part = re.sub(r'[.;:!?]', '', part)
        cleaned_parts.append(cleaned_part)

    return ','.join(cleaned_parts)

def remove_extra_spaces(text):
    # Replace multiple spaces with a single space, but keep single spaces and spaces in measurements
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):  # This part contains a number (likely a measurement)
            cleaned_parts.append(part.strip())
        else:
            cleaned_parts.append(re.sub(r' {2,}', ' ', part.strip()))
    return ','.join(cleaned_parts)

def normalize_description(description):
    if pd.isna(description):
        return ""

    normalized = str(description) # Ensure string
    normalized = remove_extra_spaces(normalized) # Fix spacing
    normalized = normalize_punctuation(normalized) # Normalize punctuation
    normalized = normalized.upper()
    return normalized

# Prepare data for training
def prepare_data(df, mlb=None):
    # Normalize descriptions
    df['normalized_description'] = df['material_description'].apply(normalize_description)

    # Extract texts
    texts = df['normalized_description'].tolist()

    # Extract labels (astm and grade)
    labels = []
    for _, row in df.iterrows():
        row_labels = []
        for label in labels_to_train:
            if pd.notna(row[label]):
                row_labels.append(f"{label}_{row[label]}")
        labels.append(row_labels)

    # Convert labels to multi-hot vectors
    if mlb is None:
        mlb = MultiLabelBinarizer()
    y = mlb.fit_transform(labels)
    label_names = mlb.classes_

    print(f"Number of unique labels: {len(label_names)}")
    print(f"Sample labels: {label_names[:5]}")

    return texts, y, label_names, mlb

class MultiLabelDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len=128):
        self.encodings = tokenizer(texts, truncation=True, padding=True, max_length=max_len)
        self.labels = torch.tensor(labels, dtype=torch.float32)

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item["labels"] = self.labels[idx]
        return item

class RobertaForMultiLabel(RobertaPreTrainedModel):
    def __init__(self, config, num_labels):
        super().__init__(config)
        self.roberta = RobertaModel(config)
        self.dropout = nn.Dropout(0.3)
        self.classifier = nn.Linear(config.hidden_size, num_labels)
        self.loss_fn = nn.BCEWithLogitsLoss()

    def forward(self, input_ids=None, attention_mask=None, labels=None):
        outputs = self.roberta(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = self.dropout(outputs.pooler_output)
        logits = self.classifier(pooled_output)

        loss = None
        if labels is not None:
            loss = self.loss_fn(logits, labels)

        from transformers.modeling_outputs import SequenceClassifierOutput
        return SequenceClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions
        )

def main():
    # Set paths
    file_path = r"src\training\data\rfq_template-example.xlsx"
    test_path = r"src\training\data\rfq_template-example.xlsx"
    output_dir = os.path.join("models", "roberta-material-classifier")
    os.makedirs(output_dir, exist_ok=True)

    # Load and prepare data
    df = load_data(file_path)
    texts, y, label_names, mlb = prepare_data(df)

    # Cross-validation setup
    from sklearn.model_selection import KFold
    import pickle
    from sklearn.metrics import (
        classification_report, accuracy_score, f1_score, precision_score,
        recall_score, precision_recall_fscore_support, confusion_matrix
    )

    n_splits = 5  # Number of folds
    cv = KFold(n_splits=n_splits, shuffle=True, random_state=42)

    # Initialize lists to store metrics across folds
    all_f1_scores = []
    all_precision_scores = []
    all_recall_scores = []
    all_exact_match = []
    all_predictions = []
    all_true_labels = []
    all_texts = []

    print(f"Performing {n_splits}-fold cross-validation")

    # For storing per-label metrics across folds
    label_metrics_across_folds = {label: {'precision': [], 'recall': [], 'f1': [], 'support': []}
                                 for label in label_names}

    # Perform cross-validation
    for fold, (train_idx, test_idx) in enumerate(cv.split(texts, y)):
        print(f"\n--- Fold {fold+1}/{n_splits} ---")

        # Split data for this fold
        fold_train_texts = [texts[i] for i in train_idx]
        fold_train_labels = y[train_idx]
        fold_test_texts = [texts[i] for i in test_idx]
        fold_test_labels = y[test_idx]

        print(f"Training on {len(fold_train_texts)} samples, testing on {len(fold_test_texts)} samples")

        # Initialize tokenizer and datasets
        tokenizer = RobertaTokenizer.from_pretrained("roberta-base")
        train_dataset = MultiLabelDataset(fold_train_texts, fold_train_labels, tokenizer)
        test_dataset = MultiLabelDataset(fold_test_texts, fold_test_labels, tokenizer)

        # Initialize model
        config = RobertaConfig.from_pretrained("roberta-base", num_labels=len(label_names))
        model = RobertaForMultiLabel.from_pretrained("roberta-base", config=config, num_labels=len(label_names))
        model.to(DEVICE)

        # Set up training arguments
        fold_output_dir = os.path.join(output_dir, f"fold_{fold+1}")
        os.makedirs(fold_output_dir, exist_ok=True)

        training_args = TrainingArguments(
            output_dir=fold_output_dir,
            num_train_epochs=5,
            per_device_train_batch_size=2,  # Further reduced batch size to avoid memory issues
            per_device_eval_batch_size=4,   # Further reduced batch size to avoid memory issues
            warmup_steps=500,
            weight_decay=0.01,
            logging_dir=os.path.join(fold_output_dir, "logs"),
            logging_steps=10,
            eval_strategy="epoch",
            save_strategy="no",  # Disable automatic saving during training to avoid file write errors
            load_best_model_at_end=False,  # Disable loading best model since we're not saving checkpoints
            fp16=False,  # Disable mixed precision to avoid memory issues
        )

        # Initialize trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=test_dataset,
        )

        # Train the model
        try:
            trainer.train()

            # Run predictions on test data
            print(f"\nRunning predictions on fold {fold+1} test data...")
            test_outputs = trainer.predict(test_dataset)
            test_preds = (torch.sigmoid(torch.tensor(test_outputs.predictions)) > 0.5).numpy()

            # Store predictions and true labels for this fold
            all_predictions.append(test_preds)
            all_true_labels.append(fold_test_labels)
            all_texts.extend(fold_test_texts)

            # Calculate metrics for this fold
            # Overall accuracy (exact match)
            exact_match = np.all(test_preds == fold_test_labels, axis=1).mean()
            all_exact_match.append(exact_match)

            # Calculate overall metrics
            f1 = f1_score(fold_test_labels, test_preds, average='weighted', zero_division=0)
            precision = precision_score(fold_test_labels, test_preds, average='weighted', zero_division=0)
            recall = recall_score(fold_test_labels, test_preds, average='weighted', zero_division=0)

            all_f1_scores.append(f1)
            all_precision_scores.append(precision)
            all_recall_scores.append(recall)

            print(f"Fold {fold+1} - Exact match: {exact_match:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")

            # Calculate per-label metrics
            precision, recall, f1, support = precision_recall_fscore_support(
                fold_test_labels, test_preds, average=None, zero_division=0
            )

            # Store per-label metrics for this fold
            for i, label in enumerate(label_names):
                label_metrics_across_folds[label]['precision'].append(precision[i])
                label_metrics_across_folds[label]['recall'].append(recall[i])
                label_metrics_across_folds[label]['f1'].append(f1[i])
                label_metrics_across_folds[label]['support'].append(support[i])
        except Exception as e:
            print(f"Error in fold {fold+1}: {str(e)}")
            print("Skipping this fold and continuing with next fold...")
            continue

    # Combine predictions from all folds
    all_predictions_combined = np.vstack(all_predictions)
    all_true_labels_combined = np.vstack(all_true_labels)

    # Calculate and print average metrics across folds
    print("\n=== Cross-Validation Results ===")
    print(f"Average Exact Match: {np.mean(all_exact_match):.4f} ± {np.std(all_exact_match):.4f}")
    print(f"Average F1 Score: {np.mean(all_f1_scores):.4f} ± {np.std(all_f1_scores):.4f}")
    print(f"Average Precision: {np.mean(all_precision_scores):.4f} ± {np.std(all_precision_scores):.4f}")
    print(f"Average Recall: {np.mean(all_recall_scores):.4f} ± {np.std(all_recall_scores):.4f}")

    # Calculate per-label metrics across all folds
    print("\nPer-label metrics (averaged across folds):")

    # Create a DataFrame for per-label metrics
    metrics_df = pd.DataFrame({
        'label': label_names,
        'precision': [np.mean(label_metrics_across_folds[label]['precision']) for label in label_names],
        'precision_std': [np.std(label_metrics_across_folds[label]['precision']) for label in label_names],
        'recall': [np.mean(label_metrics_across_folds[label]['recall']) for label in label_names],
        'recall_std': [np.std(label_metrics_across_folds[label]['recall']) for label in label_names],
        'f1-score': [np.mean(label_metrics_across_folds[label]['f1']) for label in label_names],
        'f1-score_std': [np.std(label_metrics_across_folds[label]['f1']) for label in label_names],
        'support': [np.sum(label_metrics_across_folds[label]['support']) for label in label_names]
    })

    # Calculate coverage - percentage of folds where each label appears
    metrics_df['fold_coverage'] = [
        sum(1 for s in label_metrics_across_folds[label]['support'] if s > 0) / n_splits
        for label in label_names
    ]

    # Add false positives and false negatives
    fp_list = []
    fn_list = []

    for i, label in enumerate(label_names):
        # Calculate confusion matrix for this label across all folds
        cm = confusion_matrix(all_true_labels_combined[:, i], all_predictions_combined[:, i])

        # Extract values
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()
        elif cm.shape == (1, 1):
            if all_true_labels_combined[:, i].sum() == 0:  # No positives in ground truth
                tn, fp, fn, tp = cm[0, 0], 0, 0, 0
            else:  # All positives in ground truth
                tn, fp, fn, tp = 0, 0, 0, cm[0, 0]
        else:
            tn = fp = fn = tp = 0

        fp_list.append(fp)
        fn_list.append(fn)

    metrics_df['false_positives'] = fp_list
    metrics_df['false_negatives'] = fn_list

    # Calculate error rate
    metrics_df['error_rate'] = (metrics_df['false_positives'] + metrics_df['false_negatives']) / \
                              metrics_df['support'].replace(0, 1)  # Avoid division by zero

    # Sort and print metrics
    print("\nMetrics sorted by support (most frequent labels):")
    print(metrics_df.sort_values('support', ascending=False).head(10).to_string(index=False))

    print("\nMetrics sorted by f1-score (best performing labels):")
    best_metrics = metrics_df[metrics_df['support'] > 0].sort_values('f1-score', ascending=False).head(10)
    print(best_metrics.to_string(index=False))

    print("\nMetrics sorted by f1-score (worst performing labels with support > 0):")
    poor_metrics = metrics_df[metrics_df['support'] > 0].sort_values('f1-score', ascending=True).head(10)
    print(poor_metrics.to_string(index=False))

    print("\nLabels with highest false positive rate:")
    fp_metrics = metrics_df[metrics_df['support'] > 0].sort_values('false_positives', ascending=False).head(10)
    print(fp_metrics.to_string(index=False))

    print("\nLabels with highest false negative rate:")
    fn_metrics = metrics_df[metrics_df['support'] > 0].sort_values('false_negatives', ascending=False).head(10)
    print(fn_metrics.to_string(index=False))

    # Group metrics by label type (astm vs grade)
    print("\nMetrics by label type:")
    metrics_df['label_type'] = metrics_df['label'].apply(lambda x: x.split('_')[0] if '_' in x else 'other')
    type_metrics = metrics_df.groupby('label_type').agg({
        'precision': 'mean',
        'recall': 'mean',
        'f1-score': 'mean',
        'support': 'sum',
        'false_positives': 'sum',
        'false_negatives': 'sum',
        'fold_coverage': 'mean'
    }).reset_index()
    print(type_metrics.to_string(index=False))

    # Save detailed metrics to Excel
    metrics_path = os.path.join(output_dir, "cv_label_metrics.xlsx")
    with pd.ExcelWriter(metrics_path, engine='openpyxl') as writer:
        # Save full metrics
        metrics_df.to_excel(writer, index=False, sheet_name='All Metrics')

        # Save sorted views
        metrics_df.sort_values('support', ascending=False).to_excel(
            writer, index=False, sheet_name='By Support')

        metrics_df.sort_values('f1-score', ascending=False).to_excel(
            writer, index=False, sheet_name='By F1 Score')

        metrics_df[metrics_df['support'] > 0].sort_values('false_positives', ascending=False).to_excel(
            writer, index=False, sheet_name='By False Positives')

        metrics_df[metrics_df['support'] > 0].sort_values('false_negatives', ascending=False).to_excel(
            writer, index=False, sheet_name='By False Negatives')

        metrics_df.sort_values('fold_coverage', ascending=False).to_excel(
            writer, index=False, sheet_name='By Fold Coverage')

        # Add type-based metrics
        type_metrics.to_excel(writer, index=False, sheet_name='By Label Type')

        # Format each sheet
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            worksheet.freeze_panes = 'A2'

            # Adjust column widths
            for idx, col in enumerate(metrics_df.columns if sheet_name != 'By Label Type' else type_metrics.columns):
                column_width = max(len(str(col)), 12)
                worksheet.column_dimensions[worksheet.cell(row=1, column=idx+1).column_letter].width = column_width

    print(f"Cross-validation metrics saved to: {metrics_path}")

    # Save predictions to Excel
    results_df = pd.DataFrame()
    results_df['text'] = all_texts

    # Add true labels and predictions
    for i, label in enumerate(label_names):
        results_df[f'true_{label}'] = all_true_labels_combined[:, i]
        results_df[f'pred_{label}'] = all_predictions_combined[:, i]

    # Save to Excel
    results_path = os.path.join(output_dir, "cv_test_predictions.xlsx")
    with pd.ExcelWriter(results_path, engine='openpyxl') as writer:
        results_df.to_excel(writer, index=False)

        # Format Excel
        worksheet = writer.sheets['Sheet1']
        worksheet.auto_filter.ref = worksheet.dimensions
        worksheet.freeze_panes = 'B2'  # Freeze first row and first column

        # Adjust column widths
        for idx, col in enumerate(results_df.columns):
            column_width = max(len(str(col)), 15)
            if idx == 0:  # Text column
                column_width = 50
            worksheet.column_dimensions[worksheet.cell(row=1, column=idx+1).column_letter].width = column_width

    print(f"Cross-validation predictions saved to: {results_path}")

    # Train final model on all data
    print("\n=== Training Final Model on All Data ===")

    # Initialize tokenizer and datasets for full training
    tokenizer = RobertaTokenizer.from_pretrained("roberta-base")
    full_dataset = MultiLabelDataset(texts, y, tokenizer)

    # Initialize model
    config = RobertaConfig.from_pretrained("roberta-base", num_labels=len(label_names))
    model = RobertaForMultiLabel.from_pretrained("roberta-base", config=config, num_labels=len(label_names))
    model.to(DEVICE)

    # Set up training arguments
    final_output_dir = os.path.join(output_dir, "final_model")
    os.makedirs(final_output_dir, exist_ok=True)

    training_args = TrainingArguments(
        output_dir=final_output_dir,
        num_train_epochs=5,
        per_device_train_batch_size=2,  # Further reduced batch size
        per_device_eval_batch_size=4,   # Further reduced batch size
        warmup_steps=500,
        weight_decay=0.01,
        logging_dir=os.path.join(final_output_dir, "logs"),
        logging_steps=10,
        save_strategy="no",  # Disable automatic saving during training
        fp16=False,  # Disable mixed precision
    )

    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=full_dataset,
    )

    # Train the model
    try:
        trainer.train()

        # Save the model and tokenizer manually after training is complete
        try:
            print("Saving final model...")
            # Save model components separately to avoid file write errors
            model.config.save_pretrained(final_output_dir)

            # Quantize the model to 8-bit precision to save space
            print("Quantizing model to 8-bit precision to reduce size...")
            from torch.quantization import quantize_dynamic
            quantized_model = quantize_dynamic(
                model,
                {torch.nn.Linear},
                dtype=torch.qint8
            )

            # Save the quantized model
            torch.save(quantized_model.state_dict(), os.path.join(final_output_dir, "pytorch_model_quantized.bin"))

            # Also save the original model for compatibility
            torch.save(model.state_dict(), os.path.join(final_output_dir, "pytorch_model.bin"))

            tokenizer.save_pretrained(final_output_dir)

            # Save the label binarizer
            with open(os.path.join(final_output_dir, "label_binarizer.pkl"), "wb") as f:
                pickle.dump(mlb, f)

            print(f"Final model saved to: {final_output_dir}")
        except Exception as e:
            print(f"Error saving model: {str(e)}")
            print("Continuing without saving model...")

        # Test the model on data from test_path
        print("\nTesting model on data from test file...")
        try:
            # Load test data
            test_df = load_data(test_path)
            test_texts, test_labels, _, _ = prepare_data(test_df, mlb=mlb)

            # Create a small sample for testing (first 5 items)
            sample_size = min(5, len(test_texts))
            sample_texts = test_texts[:sample_size]
            sample_labels = test_labels[:sample_size]

            print(f"Running predictions on {sample_size} samples from test data...")

            for i, (text, true_labels) in enumerate(zip(sample_texts, sample_labels)):
                # Normalize text
                text_normalized = normalize_description(text)

                # Tokenize the input
                inputs = tokenizer(text_normalized, return_tensors="pt", truncation=True, padding=True)
                inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

                # Get predictions
                with torch.no_grad():
                    outputs = model(**inputs)
                    logits = outputs.logits
                    probs = torch.sigmoid(logits).cpu().numpy()[0]
                    preds = (probs > 0.5).astype(int)

                # Print results
                print(f"\nSample {i+1} prediction:")
                print(f"Input: {text}")
                print(f"Normalized: {text_normalized}")

                print("Predicted labels:")
                for j, (label, prob, pred) in enumerate(zip(label_names, probs, preds)):
                    if pred == 1:
                        print(f"  {label}: {prob:.4f}")

                print("True labels:")
                for j, (label, is_true) in enumerate(zip(label_names, true_labels)):
                    if is_true == 1:
                        print(f"  {label}")
        except Exception as e:
            print(f"Error testing model on test data: {str(e)}")

            # Fall back to sample text if test data fails
            print("\nFalling back to sample text prediction:")
            sample_text = "ASTM A36 CARBON STEEL PLATE, 1/2 IN. THICK"
            sample_text_normalized = normalize_description(sample_text)

            # Tokenize the input
            inputs = tokenizer(sample_text_normalized, return_tensors="pt", truncation=True, padding=True)
            inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

            # Get predictions
            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                probs = torch.sigmoid(logits).cpu().numpy()[0]
                preds = (probs > 0.5).astype(int)

            # Print results
            print(f"Input: {sample_text}")
            print(f"Normalized: {sample_text_normalized}")
            print("\nPredicted labels:")

            for i, (label, prob, pred) in enumerate(zip(label_names, probs, preds)):
                if pred == 1:
                    print(f"{label}: {prob:.4f}")
    except Exception as e:
        print(f"Error training final model: {str(e)}")

if __name__ == "__main__":
    main()
