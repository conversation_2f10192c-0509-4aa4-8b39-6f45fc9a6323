{"best_global_step": 96, "best_metric": 0.9397018970189702, "best_model_checkpoint": "./multilabel-clf\\checkpoint-96", "epoch": 7.0, "eval_steps": 500, "global_step": 168, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4166666666666667, "grad_norm": 1.4755562543869019, "learning_rate": 2.7e-06, "loss": 1.4433, "step": 10}, {"epoch": 0.8333333333333334, "grad_norm": 1.3411080837249756, "learning_rate": 5.7000000000000005e-06, "loss": 1.4072, "step": 20}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6632444262504578, "eval_per_label_accuracy": 0.5972222222222222, "eval_runtime": 0.2774, "eval_samples_per_second": 346.11, "eval_steps_per_second": 43.264, "step": 24}, {"epoch": 1.25, "grad_norm": 2.584810495376587, "learning_rate": 8.7e-06, "loss": 1.3314, "step": 30}, {"epoch": 1.6666666666666665, "grad_norm": 1.3809592723846436, "learning_rate": 1.1700000000000001e-05, "loss": 1.2472, "step": 40}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.5573007464408875, "eval_per_label_accuracy": 0.8490006775067751, "eval_runtime": 0.2782, "eval_samples_per_second": 345.136, "eval_steps_per_second": 43.142, "step": 48}, {"epoch": 2.0833333333333335, "grad_norm": 1.4139870405197144, "learning_rate": 1.47e-05, "loss": 1.1574, "step": 50}, {"epoch": 2.5, "grad_norm": 1.126013159751892, "learning_rate": 1.77e-05, "loss": 1.066, "step": 60}, {"epoch": 2.9166666666666665, "grad_norm": 1.0250661373138428, "learning_rate": 2.07e-05, "loss": 0.9724, "step": 70}, {"epoch": 3.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.44352662563323975, "eval_per_label_accuracy": 0.9391937669376694, "eval_runtime": 0.2817, "eval_samples_per_second": 340.742, "eval_steps_per_second": 42.593, "step": 72}, {"epoch": 3.3333333333333335, "grad_norm": 0.923151433467865, "learning_rate": 2.37e-05, "loss": 0.8772, "step": 80}, {"epoch": 3.75, "grad_norm": 0.873205840587616, "learning_rate": 2.6700000000000002e-05, "loss": 0.783, "step": 90}, {"epoch": 4.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.3358411490917206, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2775, "eval_samples_per_second": 346.008, "eval_steps_per_second": 43.251, "step": 96}, {"epoch": 4.166666666666667, "grad_norm": 0.7562636733055115, "learning_rate": 2.97e-05, "loss": 0.6968, "step": 100}, {"epoch": 4.583333333333333, "grad_norm": 0.6410854458808899, "learning_rate": 2.6029411764705883e-05, "loss": 0.6231, "step": 110}, {"epoch": 5.0, "grad_norm": 0.599933385848999, "learning_rate": 2.161764705882353e-05, "loss": 0.5652, "step": 120}, {"epoch": 5.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.26875564455986023, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2595, "eval_samples_per_second": 369.997, "eval_steps_per_second": 46.25, "step": 120}, {"epoch": 5.416666666666667, "grad_norm": 0.5251712799072266, "learning_rate": 1.7205882352941175e-05, "loss": 0.533, "step": 130}, {"epoch": 5.833333333333333, "grad_norm": 0.5180421471595764, "learning_rate": 1.2794117647058824e-05, "loss": 0.5106, "step": 140}, {"epoch": 6.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.24464263021945953, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2813, "eval_samples_per_second": 341.253, "eval_steps_per_second": 42.657, "step": 144}, {"epoch": 6.25, "grad_norm": 0.48913446068763733, "learning_rate": 8.382352941176472e-06, "loss": 0.4926, "step": 150}, {"epoch": 6.666666666666667, "grad_norm": 0.4753621220588684, "learning_rate": 3.970588235294118e-06, "loss": 0.49, "step": 160}, {"epoch": 7.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.23850864171981812, "eval_per_label_accuracy": 0.9397018970189702, "eval_runtime": 0.2789, "eval_samples_per_second": 344.265, "eval_steps_per_second": 43.033, "step": 168}], "logging_steps": 10, "max_steps": 168, "num_input_tokens_seen": 0, "num_train_epochs": 7, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}