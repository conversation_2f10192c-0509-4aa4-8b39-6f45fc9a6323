import pandas as pd
import numpy as np

from sklearn.preprocessing import MultiLabelBinarizer
from transformers import TrainingArguments, Trainer
from sklearn.metrics import f1_score, accuracy_score
from datasets import Dataset
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from sklearn.preprocessing import LabelEncoder
import torch

USE_CUDA = True
DEVICE = "cuda" if USE_CUDA and torch.cuda.is_available() else "cpu"

def train(training_data: str | pd.DataFrame):
    # Sample data
    if isinstance(training_data, str):
        df = pd.read_excel(training_data)

    if training_data.empty:
        raise ValueError("Training data is empty")

    df = training_data

    # Encode labels
    label_encoder = LabelEncoder()
    df['label'] = label_encoder.fit_transform(df['astm'])  # 100 → 0, 200 → 1, etc.

    # Hugging Face dataset
    dataset = Dataset.from_pandas(df[['material_description', 'label']])
    dataset = dataset.train_test_split(test_size=0.2)

    model_name = "bert-base-uncased"
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    num_labels = len(label_encoder.classes_)
    model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=num_labels)
    model.to(DEVICE)

    def preprocess(example):
        return tokenizer(example["material_description"], truncation=True, padding="max_length", max_length=32)

    tokenized = dataset.map(preprocess, batched=True)

    def compute_metrics(eval_pred):
        logits, labels = eval_pred
        preds = np.argmax(logits, axis=1)
        return {"accuracy": accuracy_score(labels, preds)}

    args = TrainingArguments(
        output_dir="./clf-results",
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        num_train_epochs=5,
        logging_dir="./logs",
        eval_strategy="epoch"
    )

    trainer = Trainer(
        model=model,
        args=args,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        tokenizer=tokenizer,
        compute_metrics=compute_metrics
    )

    trainer.train()

    return model, tokenizer, label_encoder

def make_predictions(test_data, model, tokenizer, label_encoder):
    test_df = pd.read_excel(test_data)

    predictions = []

    if "astm" not in test_df:
        test_df["astm"] = None

    for row in test_df.itertuples():
        text = row.material_description
        inputs = tokenizer(text, return_tensors="pt")
        inputs.to(DEVICE)
        outputs = model(**inputs)
        pred = outputs.logits.argmax(dim=1).item()
        pred_label = label_encoder.inverse_transform([pred])[0]
        print(f"Text: {text}")
        print(f"Actual: {row.astm}, Predicted Label: {pred_label}")
        print("---")

        actual = row.astm
        pred = outputs.logits.argmax(dim=1).item()
        predictions.append({
            "text": text,
            "actual": actual,
            "predicted": pred_label
        })

    results_df = pd.DataFrame(predictions)
    results_df.to_excel("debug/astm_predictions.xlsx", index=False)
    return predictions


if __name__ == "__main__":
    # training_data = r"src\training\data\rfq_template-example.xlsx"

    training_sets = [
        r"src\training\data\rfq_template-example.xlsx",
        r"C:\Users\<USER>\Documents\GitHub\ATEM-Classifier\src\training\data\Training Heartwell1 - EXC_0016 - non fieldmap .xlsx"
    ]
    training_data = pd.concat([pd.read_excel(f) for f in training_sets])


    # test_data = r"src\training\data\rfq_template-example.xlsx"
    test_data = r"C:\Users\<USER>\Documents\GitHub\ATEM-Classifier\src\training\data\Training Heartwell1 - EXC_0016 - non fieldmap .xlsx"
    model, tokenizer, label_encoder = train(training_data)
    make_predictions(test_data, model, tokenizer, label_encoder)