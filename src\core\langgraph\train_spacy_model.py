"""
Training Script for SpaCy BOM Classification Model

Creates a custom NER model for BOM classification
"""

import nlp
from spacy.tokens import DocBin
from spacy.training import Example
import pandas as pd
import os
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
import random
from typing import List, Dict, Tuple, Any

def normalize_description(description: str) -> str:
    """Normalize material description for consistent processing"""
    # Convert to uppercase for consistency
    normalized = description.upper()

    # Replace common abbreviations
    replacements = {
        "SCH.": "SCH",
        "SCHED.": "SCH",
        "\"": "INCH",
        "INCHES": "INCH",
        "SCHEDULE": "SCH",
        "STAINLESS STEEL": "SS",
        "CARBON STEEL": "CS",
    }

    for original, replacement in replacements.items():
        normalized = normalized.replace(original, replacement)

    return normalized

def create_training_data(
    df: pd.DataFrame,
    nlp: nlp.language.Language
) -> List[Example]:
    """
    Create training examples for SpaCy NER model

    Args:
        df: DataFrame with material descriptions and annotations
        nlp: SpaCy language model

    Returns:
        List of training examples
    """
    training_examples = []

    for _, row in df.iterrows():
        # Get text and normalize
        text = row["material_description"]
        normalized_text = normalize_description(text)

        # Create doc
        doc = nlp.make_doc(normalized_text)

        # Create entities list
        entities = []

        # Extract entities from annotations
        for entity_type in ["size", "schedule", "material", "astm_standard", "component_type"]:
            if entity_type in row and pd.notna(row[entity_type]):
                entity_value = str(row[entity_type]).upper()

                # Find entity in text
                start_idx = normalized_text.find(entity_value)
                if start_idx >= 0:
                    end_idx = start_idx + len(entity_value)
                    entity_label = entity_type.upper()
                    entities.append((start_idx, end_idx, entity_label))

        # Create annotations
        annotations = {"entities": entities}
        example = Example.from_dict(doc, annotations)
        training_examples.append(example)

    return training_examples

def train_model(
    training_data: List[Example],
    output_dir: str,
    n_iter: int = 30
) -> None:
    """
    Train a SpaCy NER model

    Args:
        training_data: List of training examples
        output_dir: Directory to save the model
        n_iter: Number of training iterations
    """
    # Create a blank model or load existing
    nlp = nlp.blank("en")

    # Create the pipeline
    if "ner" not in nlp.pipe_names:
        ner = nlp.add_pipe("ner")
    else:
        ner = nlp.get_pipe("ner")

    # Add entity labels from training data
    for example in training_data:
        for ent in example.reference.ents:
            ner.add_label(ent.label_)

    # Add custom component type attribute
    if not nlp.has_pipe("component_classifier"):
        nlp.add_pipe("component_classifier", last=True)

    # Disable other pipelines during training
    pipe_exceptions = ["ner", "component_classifier"]
    other_pipes = [pipe for pipe in nlp.pipe_names if pipe not in pipe_exceptions]

    # Training loop
    with nlp.disable_pipes(*other_pipes):
        optimizer = nlp.begin_training()

        for i in range(n_iter):
            random.shuffle(training_data)
            losses = {}

            for batch in nlp.util.minibatch(training_data, size=8):
                nlp.update(batch, drop=0.35, losses=losses)

            print(f"Iteration {i+1}/{n_iter}, Losses: {losses}")

    # Save the model
    if output_dir:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        nlp.to_disk(output_dir)
        print(f"Model saved to {output_dir}")

def main(
    input_file: str,
    output_dir: str,
    n_iter: int = 30
) -> None:
    """
    Main function to train the SpaCy model

    Args:
        input_file: Path to input Excel file with annotations
        output_dir: Directory to save the model
        n_iter: Number of training iterations
    """
    print(f"SpaCy version: {nlp.__version__}")
    print(f"Loading data from {input_file}...")

    # Load the Excel file
    df = pd.read_excel(input_file)
    print(f"Loaded {len(df)} rows of data.")

    # Create blank SpaCy model
    nlp = nlp.blank("en")

    # Split into training and validation sets
    train_df, val_df = train_test_split(df, test_size=0.2, random_state=42)
    print(f"Training set: {len(train_df)} rows, Validation set: {len(val_df)} rows")

    # Create training data
    print("Creating training data...")
    training_data = create_training_data(train_df, nlp)

    # Train the model
    print("Training model...")
    train_model(training_data, output_dir, n_iter)

    print("Training complete!")

if __name__ == "__main__":
    input_file = "data/bom_annotations.xlsx"
    output_dir = "models/spacy_bom_classifier"
    main(input_file, output_dir)