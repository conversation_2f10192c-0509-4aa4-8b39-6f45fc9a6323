
import spacy
from spacy.training.example import Example
from spacy.tokens import SpanGroup

nlp = spacy.blank("en")
nlp.add_pipe("sentencizer")
span_cat = nlp.add_pipe("span_categorizer")
span_cat.add_label("PRODUCT")
span_cat.add_label("BRAND")

text = "Apple Inc. released the new Apple Watch today."
doc = nlp.make_doc(text)

# Define spans manually
spans = [
    doc.char_span(0, 5, label="BRAND"),        # Apple
    doc.char_span(31, 36, label="BRAND"),      # Apple
    doc.char_span(31, 43, label="PRODUCT")     # Apple Watch
]

# Group them by label
spans_by_label = {}
for span in spans:
    if span:
        spans_by_label.setdefault(span.label_, []).append(span)

# Add to doc
for label, spans in spans_by_label.items():
    doc.spans[label] = SpanGroup(doc, spans=spans, name=label)

# Create Example
example = Example.from_dict(doc, {"spans": spans_by_label})

# Train
nlp.initialize(lambda: [example])
for _ in range(10):
    nlp.update([example])