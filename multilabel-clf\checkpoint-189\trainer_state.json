{"best_global_step": 189, "best_metric": 0.9414762544802867, "best_model_checkpoint": "./multilabel-clf\\checkpoint-189", "epoch": 9.0, "eval_steps": 500, "global_step": 189, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.47619047619047616, "grad_norm": 0.7546043395996094, "learning_rate": 9.000000000000001e-07, "loss": 0.7246, "step": 10}, {"epoch": 0.9523809523809523, "grad_norm": 0.7620818018913269, "learning_rate": 1.9000000000000002e-06, "loss": 0.7173, "step": 20}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.7047889232635498, "eval_per_label_accuracy": 0.5104166666666666, "eval_runtime": 0.4072, "eval_samples_per_second": 353.663, "eval_steps_per_second": 22.104, "step": 21}, {"epoch": 1.4285714285714286, "grad_norm": 0.8138112425804138, "learning_rate": 2.9e-06, "loss": 0.6997, "step": 30}, {"epoch": 1.9047619047619047, "grad_norm": 0.9201421141624451, "learning_rate": 3.900000000000001e-06, "loss": 0.6716, "step": 40}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6507160663604736, "eval_per_label_accuracy": 0.6258960573476703, "eval_runtime": 0.4064, "eval_samples_per_second": 354.336, "eval_steps_per_second": 22.146, "step": 42}, {"epoch": 2.380952380952381, "grad_norm": 0.7542014122009277, "learning_rate": 4.9000000000000005e-06, "loss": 0.6475, "step": 50}, {"epoch": 2.857142857142857, "grad_norm": 0.6873849034309387, "learning_rate": 5.9e-06, "loss": 0.6242, "step": 60}, {"epoch": 3.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.5938294529914856, "eval_per_label_accuracy": 0.7786738351254481, "eval_runtime": 0.4268, "eval_samples_per_second": 337.373, "eval_steps_per_second": 21.086, "step": 63}, {"epoch": 3.3333333333333335, "grad_norm": 0.6895852088928223, "learning_rate": 6.9e-06, "loss": 0.5969, "step": 70}, {"epoch": 3.8095238095238093, "grad_norm": 0.5679261684417725, "learning_rate": 7.9e-06, "loss": 0.5704, "step": 80}, {"epoch": 4.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.5376954078674316, "eval_per_label_accuracy": 0.9089381720430108, "eval_runtime": 0.4078, "eval_samples_per_second": 353.14, "eval_steps_per_second": 22.071, "step": 84}, {"epoch": 4.285714285714286, "grad_norm": 0.5638896226882935, "learning_rate": 8.900000000000001e-06, "loss": 0.5447, "step": 90}, {"epoch": 4.761904761904762, "grad_norm": 0.5440996885299683, "learning_rate": 9.9e-06, "loss": 0.5192, "step": 100}, {"epoch": 5.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.4835628867149353, "eval_per_label_accuracy": 0.9324036738351255, "eval_runtime": 0.4067, "eval_samples_per_second": 354.09, "eval_steps_per_second": 22.131, "step": 105}, {"epoch": 5.238095238095238, "grad_norm": 0.46863749623298645, "learning_rate": 1.0900000000000002e-05, "loss": 0.4946, "step": 110}, {"epoch": 5.714285714285714, "grad_norm": 0.546397864818573, "learning_rate": 1.1900000000000001e-05, "loss": 0.4701, "step": 120}, {"epoch": 6.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.4324483871459961, "eval_per_label_accuracy": 0.938788082437276, "eval_runtime": 0.4082, "eval_samples_per_second": 352.807, "eval_steps_per_second": 22.05, "step": 126}, {"epoch": 6.190476190476191, "grad_norm": 0.45093363523483276, "learning_rate": 1.2900000000000002e-05, "loss": 0.4463, "step": 130}, {"epoch": 6.666666666666667, "grad_norm": 0.46296221017837524, "learning_rate": 1.39e-05, "loss": 0.4226, "step": 140}, {"epoch": 7.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.38387537002563477, "eval_per_label_accuracy": 0.9392361111111112, "eval_runtime": 0.4078, "eval_samples_per_second": 353.094, "eval_steps_per_second": 22.068, "step": 147}, {"epoch": 7.142857142857143, "grad_norm": 0.4984593391418457, "learning_rate": 1.4900000000000001e-05, "loss": 0.3982, "step": 150}, {"epoch": 7.619047619047619, "grad_norm": 0.42600300908088684, "learning_rate": 1.5900000000000004e-05, "loss": 0.3749, "step": 160}, {"epoch": 8.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.33771419525146484, "eval_per_label_accuracy": 0.938788082437276, "eval_runtime": 0.4448, "eval_samples_per_second": 323.712, "eval_steps_per_second": 20.232, "step": 168}, {"epoch": 8.095238095238095, "grad_norm": 0.41924944519996643, "learning_rate": 1.69e-05, "loss": 0.3544, "step": 170}, {"epoch": 8.571428571428571, "grad_norm": 0.4304155111312866, "learning_rate": 1.79e-05, "loss": 0.3312, "step": 180}, {"epoch": 9.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.2982037365436554, "eval_per_label_accuracy": 0.9414762544802867, "eval_runtime": 0.4028, "eval_samples_per_second": 357.521, "eval_steps_per_second": 22.345, "step": 189}], "logging_steps": 10, "max_steps": 210, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}