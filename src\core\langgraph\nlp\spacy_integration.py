"""
SpaCy Integration Layer for BOM Classification Workflow

Replaces LLM-based classification with SpaCy NER models
"""

import nlp
from typing import Dict, Any, Optional, List
from pathlib import Path
import os

class SpacyModelHandler:
    """Handler for SpaCy models in the classification workflow"""

    def __init__(self, model_dir: str = None, debug_mode: bool = False):
        self.model_dir = model_dir or os.environ.get("SPACY_MODEL_DIR", "models/spacy_bom_classifier")
        self.debug_mode = debug_mode
        self.nlp = None
        self._load_model()

    def _load_model(self):
        """Load the SpaCy model"""
        try:
            if Path(self.model_dir).exists():
                self.nlp = nlp.load(self.model_dir)
                if self.debug_mode:
                    print(f"✅ Loaded SpaCy model from {self.model_dir}")
            else:
                # Fallback to blank model if custom model not found
                self.nlp = nlp.blank("en")
                if self.debug_mode:
                    print(f"⚠️ Custom model not found. Using blank model.")
        except Exception as e:
            print(f"❌ Error loading SpaCy model: {e}")
            self.nlp = nlp.blank("en")

    def classify_item(self, material_description: str) -> Dict[str, Any]:
        """
        Classify a material description using SpaCy NER

        Args:
            material_description: The material description to classify

        Returns:
            Dictionary with extracted entities and classifications
        """
        if not self.nlp:
            return {"error": "Model not loaded"}

        # Process with SpaCy
        doc = self.nlp(material_description)

        # Extract entities
        entities = {ent.label_: ent.text for ent in doc.ents}

        # Determine primary category
        primary_category = self._determine_category(doc, entities)

        return {
            "extracted_properties": entities,
            "processing_path": primary_category,
            "confidence_scores": {"category_determination": 0.9 if primary_category != "unknown" else 0.5}
        }

    def _determine_category(self, doc, entities: Dict[str, str]) -> str:
        """Determine the primary category based on entities"""
        # Use custom attributes if available
        if doc.has_extension("component_type") and doc._.component_type:
            return doc._.component_type.lower()

        # Rule-based category determination
        if "COMPONENT_TYPE" in entities:
            component_type = entities["COMPONENT_TYPE"].lower()
            category_map = {
                "elbow": "fitting",
                "tee": "fitting",
                "reducer": "fitting",
                "coupling": "fitting",
                "nipple": "fitting",
                "valve": "valve",
                "ball valve": "valve",
                "gate valve": "valve",
                "flange": "flange",
                "pipe": "pipe"
            }

            for key, category in category_map.items():
                if key in component_type:
                    return category

        # Fallback to keyword matching
        pipe_keywords = ["pipe", "smls", "seamless", "erw"]
        fitting_keywords = ["elbow", "tee", "reducer", "coupling", "nipple", "cap"]
        valve_keywords = ["valve", "ball", "gate", "globe", "check"]
        flange_keywords = ["flange", "wn", "so", "blind", "rtj"]

        text_lower = doc.text.lower()

        if any(keyword in text_lower for keyword in pipe_keywords):
            return "pipe"
        elif any(keyword in text_lower for keyword in fitting_keywords):
            return "fitting"
        elif any(keyword in text_lower for keyword in valve_keywords):
            return "valve"
        elif any(keyword in text_lower for keyword in flange_keywords):
            return "flange"

        return "unknown"