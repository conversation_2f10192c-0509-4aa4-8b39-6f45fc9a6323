"""
Normalize Description and grab exact matches, spans which are used for training.
"""
import re
import spacy
import json
from spacy.matcher import Phrase<PERSON>atcher

import pandas as pd
from pprint import pprint
# from spacy.tokens import DocBin
from collections import defaultdict

from openpyxl.comments import Comment
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, PatternFill

from src.core.normalize_description.bom_patterns import SchedulePatterns, RatingPatterns
from src.core.normalize_description.normalize_end_types import create_end_tags
from src.core.normalize_description.normalize_astm import create_astm_tags
from src.core.categorization_table import categorization_table

'''
- Improved and made more modular: Schedule,
To do:
    - Add General Option for Odd Radius
    - Ignore short prepostions when checking (TO/AND/)
    - Implement Confidence mechanism
    - Replace 'Deg', LR, SR, Thk, Cert, Frgd (Forged), SMLS, Wld, Npl, Galv
    - Integrate Logic for flagging unconfident results
        (RATING EX.: SPRING CAN FIG. PTP-1, SIZE 100, TYPE F, REFER TO DATA SHEET (08-38404) | -> | SPRING CAN FIG. PTP-1,CLASS 100,TYPE F,REFER TO DATA SHEET (08-38404) )
    - Implement Coating and Forging
Schedule: Format for "SCH {sch} X SCH {sch} (Axis 020, EXC_014)
    - Integrate cross schedule patterns like: 'SCH1 SCH2'
ASTM:
    - Flag standalone values
    - Make sure values picked up on rating or is a value in another list is ignored (300 LB registers as A300 and says exact match. Should be standalone)
        EX: ORIFICE WN, 300 LB, RF, FPT, ASME B16.36 | rating:300,astm:A300,ends:FTE | rating:300,astm:Exact match,ends:Exact
        EX2: BALL VALVE, CL 150, FL, RF, FULL BORE, 316 SS BODY AND STEM
    - Handle cases where ASTM is actual a tech standard: "TEE, CARBON STEEL TO ASTM B16.11 CLASS 3000, SOCKET WELD"
Grade:
    - Add ' WP-W 304L'
    - Replace "GR" with GRADE if standalone
    - Handle different formats of 304L, WP-304L, etc better
    Edge Cases:
    - Handle 'DG' (Dual Grade and ensure we skip these).
        Example: "90dg Ell B16.11 cl3000 SW A182-F304L/DG F304/F304L Frgd" in Brock BRS_0000 for reference
    - Handle '{Grade Value/'GRADE'}' (Look immediatelty after to see if there is a Dual Grade ).
        Example: "Gate Vlv Extd Body OS&Y Rising Stem Red Prt Bonnet: Wld Cl800 BWxNPTF ,
            A182 GR F316/Grade F316/316L DG 316/316L Opr HW , API Trim #12/12A" in Brock BRS_0000 for reference
Coating:
    - "RPTFE" (Reinforced PTFE (Polytetrafluoroethylene)), Key words: "Coated", "Finish"
Rating:
    - Add logic for rating in the format "#rating', ex. #150, #3000, etc
    - Add logic to check for {rating} PSIG and {rating} PSI (Currently registers as a standalone value)
    - Rating match type is showing value (see ASTM example above)
Ends:
    - Add MTE (Male Threaded) and FTE (Female Threaded), MTBE and FTBE (Likely BE = Both Ends), NPTFE
    - Convert FF/RF to FLG Ends
    - Add FLG X MGE (FF X MGE and RF X MGE. What is that? PE X MGE)
        Claude "The "MGE" likely stands for "Mechanical Grooved End" in this context,
        especially since your example mentions "Victaulic Grooved" which is a common
        mechanical groove coupling system used in piping."
    - "MIP X FIP" means:
        MIP = Male Iron Pipe thread
        X = by/to (connecting one end to the other)
        FIP = Female Iron Pipe thread
    - FE X CTE
    - Add whole word logic (Socket Weld End(s), Butt Weld End(s), BEVELED END(S), PLAIN END, THREADED ENDS, and other variations)
    - If Tee, and now Cross type found, manually insert
    - Prevent end x end x end. If more than one, prioritize the actual cross pattern
    - MPTFE is FExTE?
    - Make sure SW is not part of SW Flange

FLANGE/VALVE:
    - Spell check common mistakes (RASIED, FLANE, LAROLET)
    - Correct Abbreviations: (Flg/Flg., Con/Cocc, Cplg, Eq. Tee, Hvy, Red., SO, WN, SW, Sp. (Spiral Wound), Std Blt
Handle 'SCHEDULE TO MATCH PIPE' at the end of a merge to map the schedule
'''

debug_variations = False

#### TEST FUNCTIONS
def generate_variations(word):
    variations = set()
    variations.add(word)
    variations.add(word.upper())
    variations.add(word.lower())
    variations.add(word.replace(' ', ''))
    variations.add(word.replace(' ', '.'))
    variations.add(word.replace(' ', '-'))
    variations.add(re.sub(r'([A-Z])', r' \1', word).strip())  # Add spaces before capital letters
    return variations

def test_schedule_normalization():
    test_cases = [
        # Standard Schedule Values with Different Prefixes
        "PIPE,A312,SCH 40,01.500",
        "PIPE,A312,SCHEDULE 80,01.500",
        "PIPE,A312,SCH. 120,01.500",
        "PIPE,A312,SCHED 160,01.500",
        "PIPE,A312,S/10S,01.500",

        # Standalone Values
        "PIPE,A312,40,01.500",
        "PIPE,A312,80S,01.500",
        "PIPE,A312,10S,01.500",

        # Special Schedule Types
        "PIPE,A312,STD,01.500",
        "PIPE,A312,STD WT,01.500",
        "PIPE,A312,STW,01.500",
        "PIPE,A312,XS,01.500",
        "PIPE,A312,XH,01.500",
        "PIPE,A312,XXS,01.500",
        "PIPE,A312,XXH,01.500",

        # Cross Schedule Formats with Different Delimiters
        "PIPE,A312,40 X 80,01.500",
        "PIPE,A312,40/80,01.500",
        "PIPE,A312,40-80,01.500",
        "PIPE,A312,STD X 40,01.500",
        "PIPE,A312,10S/40S,01.500",
        "PIPE,A312,XS-XXS,01.500",
        "PIPE,A312,S40/STW,01.500",

        # Custom Schedule Variations
        "PIPE,A312,CUSTOM-H,01.500",
        "PIPE,A312,CUSTOM H,01.500",
        "PIPE,A312,CUST.H,01.500",
        "PIPE,A312,CUST-L,01.500",
        "PIPE,A312,CUSTOM.M,01.500",
        "PIPE,A312,CUST M,01.500",

        # Mixed Cases
        "PIPE,A312,sch40,01.500",
        "PIPE,A312,ScH. 80,01.500",
        "PIPE,A312,Std wt,01.500",
        "PIPE,A312,Custom-h,01.500",

        # Edge Cases and Invalid Formats (should not be tagged)
        "PIPE,A312,316,01.500",  # Number that looks like schedule
        "PIPE,A312,S-316,01.500",  # Invalid prefix
        "PIPE,A312,SCH 4 X 3,01.500",  # Invalid cross schedule
        "PIPE,A312,SCHEDULE,01.500",  # Missing number
        "PIPE,A312,S/,01.500",  # Missing number
        "PIPE,A312,40 X,01.500",  # Incomplete cross
        "PIPE,A312,STD X STW,01.500",  # Mixed special types
        "PIPE,A312,CUSTOM,01.500",  # Incomplete custom
        "PIPE,A312,CUST-X,01.500",  # Invalid custom

        # Complex Cases
        "PIPE,A312 316/316L SST,EFW,PE,SCH40/80,SCHED.10S X STD,01.500",
        "PIPE,A312,SCH. 40S-STD WT,CUSTOM-H,01.500",
        "PIPE,A312,s/40 X std wt,cust.m,01.500"
    ]

    print("Testing schedule normalization:")
    for test in test_cases:
        normalized, tag = normalize_schedule(test)
        print(f"\nOriginal: {test}")
        print(f"Normalized: {normalized}")
        print(f"Tag: {tag}")
#### TEST FUNCTIONS


def get_options_by_field_as_list(field_name):
    """
    Retrieve the options list for a given field name from the categorization table.

    Parameters:
        categorization_table (list): A list of dictionaries containing field, description, and options.
        field_name (str): The name of the field for which options are to be retrieved.

    Returns:
        list: The options for the specified field, or an empty list if the field is not found.

    Example Usage:
        field_name = "unit_of_measure"
        options = get_options_by_field(categorization_table, field_name)
        print(options)  # Output: ["Cubic Feet", "Each", "LBS", "Linear Feet", "Lot", "Pair", "Square Feet", "Tons"]
    """
    for entry in categorization_table:
        if entry["field"] == field_name:
            return entry.get("options", [])
    return []  # Return an empty list if the field is not found.

def get_all_options_as_set(categorization_table, field_name):
    """
    Retrieve the options list for a given field name and format it as a Python set.

    Parameters:
        categorization_table (list): A list of dictionaries containing field, description, and options.
        field_name (str): The name of the field for which options are to be retrieved.

    Returns:
        set: A set of options for the specified field, or an empty set if the field is not found.

    Example Usage:
        VALID_ASTM = get_all_options_as_set(categorization_table, "astm")
    """
    for entry in categorization_table:
        if entry["field"] == field_name:
            return set(entry.get("options", []))  # Return the options as a set
    return set()  # Return an empty set if the field is not found

def remove_extra_spaces(text):
    # Replace multiple spaces with a single space, but keep single spaces and spaces in measurements
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):  # This part contains a number (likely a measurement)
            cleaned_parts.append(part.strip())
        else:
            cleaned_parts.append(re.sub(r' {2,}', ' ', part.strip()))
    return ','.join(cleaned_parts)

def normalize_punctuation(text):
    # Replace multiple commas with a single comma
    text = re.sub(r',{2,}', ',', text)

    # Remove punctuation except for commas and decimal points in numbers
    parts = text.split(',')
    cleaned_parts = []
    for part in parts:
        if any(char.isdigit() for char in part):
            # This part contains a number, so preserve the decimal point
            cleaned_part = re.sub(r'[;:!?]', '', part)
        else:
            # This part doesn't contain a number, so remove all punctuation except commas
            cleaned_part = re.sub(r'[.;:!?]', '', part)
        cleaned_parts.append(cleaned_part)

    return ','.join(cleaned_parts)

def normalize_schedule(text):
    """
    Normalize various schedule formats in text to a standard format.
    Returns tuple of (normalized_text, metadata_tag)
    """
    found_schedule = None  # Store found schedule value
    patterns = SchedulePatterns()

    # Debug flag
    DEBUG_SCHEDULE = True

    def debug_schedule(section, message):
        """Print debug information for schedule extraction"""
        if DEBUG_SCHEDULE:
            print(f"\n[SCHEDULE {section}] {message}")

    # Function to check if a value is a valid schedule
    def is_valid_schedule(value):
        """Validate if a schedule value is in our allowed list or is a valid decimal"""
        # Check if it's in our predefined list
        if value.upper() in patterns.VALID_SCHEDULES:
            debug_schedule("VALID", f"'{value}' is in valid schedules list")
            return True

        # Check if it's a valid decimal (for wall thickness)
        if re.match(r'^\d+\.\d+$', value):
            debug_schedule("VALID", f"'{value}' is a valid decimal (wall thickness)")
            return True

        # Check if it's a valid integer
        if re.match(r'^\d+$', value):
            # Exclude small numbers that are likely to be sizes, not schedules
            if int(value) >= 5:  # Most valid schedules start from 5
                debug_schedule("VALID", f"'{value}' is a valid integer schedule")
                return True
            else:
                debug_schedule("INVALID", f"'{value}' is too small to be a valid schedule")
                return False

        debug_schedule("INVALID", f"'{value}' is not a valid schedule")
        return False

    # Add a function to check if a pattern looks like a size specification
    def is_likely_size(text, match):
        """Check if the matched pattern is likely a size specification rather than a schedule"""
        # Defensive: ensure match is not None and has at least 2 groups
        if match is None or not hasattr(match, 'lastindex') or match.lastindex is None or match.lastindex < 2:
            debug_schedule("SIZE CHECK", "Match object is None or does not have at least 2 groups; skipping size check.")
            return False

        # Check for common size indicators near the match
        size_indicators = ['DIA', 'DIAMETER', 'SIZE', 'DN', 'NPS', '"', 'INCH', 'MM']
        context = text[max(0, match.start() - 20):min(len(text), match.end() + 20)]

        for indicator in size_indicators:
            if indicator in context.upper():
                debug_schedule("SIZE CHECK", f"Found size indicator '{indicator}' near match")
                return True

        # Check if the pattern looks like a pipe size (e.g., "3 x 4")
        if match.group(1).isdigit() and match.group(2).isdigit():
            if int(match.group(1)) < 5 and int(match.group(2)) < 5:
                debug_schedule("SIZE CHECK", f"Pattern '{match.group(1)} x {match.group(2)}' looks like a pipe size")
                return True

        return False

    # Look for explicit SCH x SCH patterns first (highest priority for cross schedules)
    # This pattern handles formats like "SCH 10S X SCH 40S", "SCH 10S TO SCH 40S", etc.
    explicit_cross_pattern = r'\bSCH(?:EDULE)?\.?\s+(\w+(?:\.\d+)?)\s+(?:X|x|TO|AND|BY)\s+SCH(?:EDULE)?\.?\s+(\w+(?:\.\d+)?)\b'

    # Also handle formats with no space between separator like "SCH 10S/SCH 40S" or "SCH 10S-SCH 40S"
    explicit_cross_pattern2 = r'\bSCH(?:EDULE)?\.?\s+(\w+(?:\.\d+)?)[\/\-]SCH(?:EDULE)?\.?\s+(\w+(?:\.\d+)?)\b'
    # Try the first pattern (with spaces around separator)
    match = re.search(explicit_cross_pattern, text, re.IGNORECASE)
    if match:
        debug_schedule("PATTERN", f"Found explicit SCH x SCH pattern: '{match.group(0)}'")
        sch1 = match.group(1).upper()
        sch2 = match.group(2).upper()

        if is_valid_schedule(sch1) and is_valid_schedule(sch2):
            found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
            text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
            debug_schedule("MATCH", f"Matched explicit SCH x SCH pattern to '{sch1} x {sch2}'")

    # If no match found, try the second pattern (without spaces around separator)
    if not found_schedule:
        match = re.search(explicit_cross_pattern2, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found explicit SCH/SCH pattern: '{match.group(0)}'")
            sch1 = match.group(1).upper()
            sch2 = match.group(2).upper()

            if is_valid_schedule(sch1) and is_valid_schedule(sch2):
                found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                debug_schedule("MATCH", f"Matched explicit SCH/SCH pattern to '{sch1} x {sch2}'")

    # Handle SCH1/SCH2 pattern (e.g., "SCH1-XS SCH2-XS")
    if not found_schedule:
        sch_pattern = r'\bSCH1-(\w+(?:\.\d+)?)\s+SCH2-(\w+(?:\.\d+)?)\b'
        match = re.search(sch_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found SCH1/SCH2 pattern: '{match.group(0)}'")
            sch1 = match.group(1).upper()
            sch2 = match.group(2).upper()
            if is_valid_schedule(sch1) and is_valid_schedule(sch2):
                found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                debug_schedule("MATCH", f"Matched SCH1/SCH2 pattern to '{sch1} x {sch2}'")

    # Handle SCH1 {value} SCH2 {value} pattern
    if not found_schedule:
        sch_alt_pattern = r'\bSCH(?:EDULE)?\s*1\s+(\w+(?:\.\d+)?)\s+SCH(?:EDULE)?\s*2\s+(\w+(?:\.\d+)?)\b'
        match = re.search(sch_alt_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found SCH1/SCH2 alternate pattern: '{match.group(0)}'")
            sch1 = match.group(1).upper()
            sch2 = match.group(2).upper()
            if is_valid_schedule(sch1) and is_valid_schedule(sch2):
                found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                debug_schedule("MATCH", f"Matched SCH1/SCH2 alternate pattern to '{sch1} x {sch2}'")

    # If no SCH1/SCH2 pattern found, try S-prefix patterns
    if not found_schedule:
        s_pattern = r'\bS-(\w+(?:\.\d+)?)\s+S-(\w+(?:\.\d+)?)\b'
        match = re.search(s_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found S-prefix pattern: '{match.group(0)}'")
            sch1 = match.group(1).upper()
            sch2 = match.group(2).upper()
            if is_valid_schedule(sch1) and is_valid_schedule(sch2) and not is_likely_size(text, match):
                found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                debug_schedule("MATCH", f"Matched S-prefix pattern to '{sch1} x {sch2}'")

    # If no S-prefix pattern found, try S-prefix with cross separator
    if not found_schedule:
        s_cross_pattern = r'\bS-(\w+(?:\.\d+)?)\s+(?:X|x|TO|AND|BY)\s+S-(\w+(?:\.\d+)?)\b'
        match = re.search(s_cross_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found S-prefix cross pattern: '{match.group(0)}'")
            sch1 = match.group(1).upper()
            sch2 = match.group(2).upper()
            if is_valid_schedule(sch1) and is_valid_schedule(sch2) and not is_likely_size(text, match):
                found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                debug_schedule("MATCH", f"Matched S-prefix cross pattern to '{sch1} x {sch2}'")

    # If no dual pattern found, try single S-prefix
    if not found_schedule:
        single_s_pattern = r'\bS-(\w+(?:\.\d+)?)\b'
        match = re.search(single_s_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found single S-prefix pattern: '{match.group(0)}'")
            sch = match.group(1).upper()
            if is_valid_schedule(sch) and not is_likely_size(text, match):
                found_schedule = sch.lower()
                text = text[:match.start()] + f"SCH {sch}" + text[match.end():]
                debug_schedule("MATCH", f"Matched single S-prefix pattern to '{sch}'")

    # If still no match, try standard prefixed formats
    if not found_schedule:
        for prefix in patterns.PREFIX_PATTERNS:
            prefix_pattern = rf'\b{prefix}\s*(\w+(?:\.\d+)?)'
            match = re.search(prefix_pattern, text, re.IGNORECASE)
            if match:
                debug_schedule("PATTERN", f"Found standard prefix pattern: '{match.group(0)}'")
                sch = match.group(1).upper()
                if is_valid_schedule(sch) and not is_likely_size(text, match):
                    found_schedule = sch.lower()
                    text = text[:match.start()] + f"SCH {sch}" + text[match.end():]
                    debug_schedule("MATCH", f"Matched standard prefix pattern to '{sch}'")
                    break

    # Note: We've moved the explicit SCH x SCH pattern to the top of the function for higher priority

    # If still no match, look for cross formats with various separators
    if not found_schedule:
        # Improved pattern to better match standalone schedule values with separators
        # Focus on valid schedule values like 10S, 40S, XS, etc. rather than any numbers
        for separator in [' x ', ' X ', '/', '-']:
            # Use a more specific pattern that targets known schedule formats
            cross_pattern = rf'\b((?:\d+S?|STD|XS|XXS|XH|XXH))\s*{re.escape(separator)}\s*((?:\d+S?|STD|XS|XXS|XH|XXH))\b'
            match = re.search(cross_pattern, text, re.IGNORECASE)
            if match:
                debug_schedule("PATTERN", f"Found cross format pattern: '{match.group(0)}'")
                sch1 = match.group(1).upper()
                sch2 = match.group(2).upper()

                # Extra validation to avoid false positives
                # Only accept if both values are valid schedules and not likely sizes
                if (is_valid_schedule(sch1) and is_valid_schedule(sch2) and
                    not is_likely_size(text, match)):

                    found_schedule = f"{sch1.lower()}_x_{sch2.lower()}"
                    text = text[:match.start()] + f"SCH {sch1} x {sch2}" + text[match.end():]
                    debug_schedule("MATCH", f"Matched cross format pattern to '{sch1} x {sch2}'")
                    break
                else:
                    debug_schedule("REJECTED", f"Cross format '{match.group(0)}' failed validation")

    # Look for wall thickness specifications
    if not found_schedule:
        wall_pattern = r'\bWALL\s+(?:THICKNESS|THK)?\s*(?:=|:)?\s*(\d+\.\d+)(?:\s*(?:"|IN|MM))?\b'
        match = re.search(wall_pattern, text, re.IGNORECASE)
        if match:
            debug_schedule("PATTERN", f"Found wall thickness pattern: '{match.group(0)}'")
            thickness = match.group(1)
            found_schedule = thickness
            text = text[:match.start()] + f"SCH {thickness}" + text[match.end():]
            debug_schedule("MATCH", f"Matched wall thickness pattern to '{thickness}'")

    # Final pass: Clean up formatting
    for pattern, replacement in patterns.CLEANUP_PATTERNS:
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

    # Return tuple of (normalized_text, metadata_tag)
    if found_schedule:
        tag_value = found_schedule.replace('_x_', ' x ')  # Replace the underscore pattern with space-x-space
        tag_value = tag_value.upper()  # Uppercase everything
        tag_value = tag_value.replace(' X ', ' x ')  # Make sure the 'x' is lowercase
        debug_schedule("FINAL RESULT", f"Schedule: {tag_value}")
        return text, f"schedule:{tag_value}"

    debug_schedule("FINAL RESULT", "No schedule found")
    return text, None

def test_sch_patterns():
    test_cases = [
        # SCH1-XS SCH2-XS patterns
        "CONCENTRIC SWAGE MSS SP-95 - A234-WPB PE SMLS SCH1-XS SCH2-XS R21FQC04ZZ01",
        "ECCENTRIC SWAGE MSS SP-95 - A105 BE PE SMLS SCH1-XS SCH2-XS R22FQC01VZ01",
        "ECCENTRIC SWAGE MSS SP-95 - A182-F316/316L DUAL GR PE SMLS SCH1-40S SCH2-40S R22FQL1MZZ04",
        "ECCENTRIC SWAGE MSS SP-95 - A234-WPB BE MTE SMLS SCH1-STD SCH2-XS R22FQC04VW15",
        "ECCENTRIC SWAGE MSS SP-95 - A234-WPB BE MTE SMLS SCH1-XS SCH2-XS R22FQC04VW01",

        # S-prefix patterns
        "ECCENTRIC REDUCER ASME B16.9 A234-WPB BE SMLS C22DC04V01 S-60 S-XS",
        "ECCENTRIC REDUCER ASME B16.9 A234-WPB BE SMLS C22DC04V01 S-XS",
        "ECCENTRIC REDUCER ASME B16.9 A420-WPL6 BE SMLS C22DC09V01 S-15.88 S-XS",

        # S-prefix cross patterns
        "Ecc Swage SP-95 BBE A234-WPB Smls S-STD x S-XS",
        "ECCENTRIC REDUCER ASME B16.9 A234-WPB BE SMLS C22DC04V01 S-60 x S-XS",
        "ECCENTRIC REDUCER ASME B16.9 A234-WPB BE SMLS C22DC04V01 S-STD TO S-XS",

        # Mixed patterns (to ensure we don't miss other schedule formats)
        "PIPE, A312, SCH 40, 01.500",
        "PIPE, A312, 40/80, 01.500",
        "PIPE, A312, XS-XXS, 01.500",

        # Edge cases
        "PIPE WITH NO SCHEDULE INFORMATION",
        "SCH1-40 MISSING SECOND PART",
        "S-40 SINGLE S PREFIX",
        "MULTIPLE PATTERNS SCH1-40 SCH2-80 AND ALSO S-60 S-XS"
    ]

    print("Testing SCH pattern normalization:")
    for test in test_cases:
        normalized, tag = normalize_schedule(test)
        print(f"\nOriginal: {test}")
        print(f"Normalized: {normalized}")
        print(f"Tag: {tag}")

def normalize_rating(text, review=False):

    """
    Normalize various rating formats in text to a standard format.
    Returns tuple of (normalized_text, metadata_tag, review_tag, match_type)
    """


    found_rating = None  # Store found rating value
    review_tag = None
    match_type = None  # Track which pattern matched
    patterns = RatingPatterns()

    # Add specific patterns for hash rating formats
    hash_rating_pattern = r'#\s*(\d+)'  # For #3000 format
    rating_hash_pattern = r'(\d+)\s*#'  # For 300# format

    # Helper function to validate rating values
    def validate_rating(value):
        """Validate if a rating value is in our allowed list"""
        # Strip any non-numeric characters and check if base number is valid
        base_number = ''.join(filter(str.isdigit, value))
        return base_number in patterns.VALID_RATINGS

    def replace_rating(match):
        """Handle rating replacement for prefixed formats"""
        nonlocal found_rating, match_type

        # Try all capture groups (prefix-number, number-suffix, and hash-number patterns)
        rating_num = None
        for group_num in [1, 2, 3]:  # Check all possible capture groups
            if match.group(group_num):
                rating_num = ''.join(filter(str.isdigit, match.group(group_num)))
                break

        if rating_num and validate_rating(rating_num):
            found_rating = rating_num
            match_type = "prefixed"
            return f"CLASS {rating_num}"
        return match.group(0)

    def replace_standalone(match):
        nonlocal found_rating, review_tag, match_type
        value = match.group(1).strip().upper()
        rating_num = ''.join(filter(str.isdigit, value))  # Extract just the numbers

        if validate_rating(rating_num):
            found_rating = rating_num
            match_type = "standalone"

            if review:
                # Mark standalone matches for review
                review_tag = f"rating: Unconfident Match (standalone)"
            return f",CLASS {rating_num},"
        return match.group(0)

    # First pass: Handle prefixed formats
    text = re.sub(patterns.get_rating_pattern(),
                 replace_rating,
                 text,
                 flags=re.IGNORECASE)

    # Special pass: Handle #rating format if no match found yet
    if not found_rating:
        def replace_hash_rating(match):
            nonlocal found_rating, match_type
            rating_num = match.group(1)
            if validate_rating(rating_num):
                found_rating = rating_num
                match_type = "hash_rating"
                return f"CLASS {rating_num}"
            return match.group(0)

        text = re.sub(hash_rating_pattern,
                     replace_hash_rating,
                     text,
                     flags=re.IGNORECASE)

    # Special pass: Handle rating# format if no match found yet
    if not found_rating:
        def replace_rating_hash(match):
            nonlocal found_rating, match_type
            rating_num = match.group(1)
            if validate_rating(rating_num):
                found_rating = rating_num
                match_type = "rating_hash"
                return f"CLASS {rating_num}"
            return match.group(0)

        text = re.sub(rating_hash_pattern,
                     replace_rating_hash,
                     text,
                     flags=re.IGNORECASE)

    # Second pass: Handle standalone values if no prefixed match found
    if not found_rating:
        text = re.sub(patterns.get_standalone_pattern(),
                     replace_standalone,
                     text,
                     flags=re.IGNORECASE)

    # Third pass: Clean up formatting
    for pattern, replacement in patterns.CLEANUP_PATTERNS:
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

    # Build metadata tag with match type
    metadata_tag = f"rating:{found_rating}" if found_rating else None
    match_type = f"rating:{found_rating}" if found_rating else None

    return text, metadata_tag, review_tag, match_type

    # Return tuple of (normalized_text, metadata_tag)
    # return text, f"rating:{found_rating}" if found_rating else None, review_tag

def normalize_astm(text, review=False):
    """
    Normalize ASTM values in text to a standard format.
    Returns tuple of (normalized_text, metadata_tag, review_tag, match_type)
    """
    VALID_ASTM = get_all_options_as_set(categorization_table, "astm")
    VALID_GRADES = get_all_options_as_set(categorization_table, "grade")

    found_astm = None
    found_grade = None
    review_tag = None
    match_type = None

    # Define core ASTM pattern to be used consistently
    # Modified to prevent matching B16.xx standards which are ASME, not ASTM
    ASTM_VALUE_PATTERN = r'[ABF]\s*[-]?\s*\d+(?!\.)(?:\.\d+)?'  # Matches: A106, F-440, but not B16.11

    # Define pattern for combined ASTM/Grade values like TP304L/304L
    # Improved pattern to better capture compound grades
    COMBINED_PATTERN = r'\b([A-Z]+\d+[A-Z]*(?:/[A-Z0-9]+[A-Z]*)+)(?:-[A-Z])?\b'

    # Debug flag for compound grades
    DEBUG_COMPOUND = True

    def debug_compound(section, message):
        """Print debug information for compound grades"""
        if DEBUG_COMPOUND:
            print(f"\n[COMPOUND {section}] {message}")

    # Define pattern for ASTM with grade suffix like A234-WPB-S
    # Improved pattern to better handle compound grades like A403-WP304/304L-W
    # Modified to avoid matching B16.11 as ASTM B16.1 with grade 1
    ASTM_GRADE_SUFFIX_PATTERN = r'\b(ASTM\s+)?([ABF]\s*[-]?\s*\d+(?!\.)(?:\.\d+)?)\s*[-]?\s*([A-Z][A-Z0-9]*(?:/[A-Z0-9]+)*)(?:[-]([A-Z]))?\b'

    # Define pattern for grades with GR. prefix
    # Improved pattern to better handle various formats of grade designations
    GR_PREFIX_PATTERN = r'\b(?:GR\.?|GRADE)\s*\.?\s*([A-Z0-9/-]+)\b'

    # Helper function to normalize text for comparison
    def normalize_text_for_comparison(text):
        """Remove spaces, dots, hyphens, etc. and convert to uppercase"""
        return re.sub(r'[\s.-]', '', text.upper())

    def find_best_astm_match(potential_value):
        """Find the best matching valid ASTM value"""
        cleaned = normalize_text_for_comparison(potential_value)

        # Ignore B16.xx standards which are ASME, not ASTM
        if re.match(r'B16\d+', cleaned):
            return None

        # First try exact match
        if cleaned in VALID_ASTM:
            return cleaned

        # Try to match base ASTM number (e.g., A234 from A234WPB)
        base_match = re.match(r'([A-Z]\d+)', cleaned)
        if base_match:
            base_value = base_match.group(1)
            if base_value in VALID_ASTM:
                return base_value

        # Handle special cases
        if cleaned.startswith('A') and any(valid.startswith(cleaned) for valid in VALID_ASTM):
            matching = [valid for valid in VALID_ASTM if valid.startswith(cleaned)]
            return min(matching, key=len)  # Return shortest matching value

        return None

    def find_best_grade_match(potential_value):
        """Find the best matching valid grade value"""
        if not potential_value or len(potential_value.strip()) < 1:
            return None

        # Try exact match
        if potential_value in VALID_GRADES:
            debug_compound("EXACT MATCH", f"Found exact match for '{potential_value}'")
            return potential_value

        # Check if this is a compound grade with '/' or '-'
        if ('/' in potential_value or '-' in potential_value) and not potential_value.startswith('GR'):
            debug_compound("REFINE", f"Detected potential compound grade: '{potential_value}'")

            # Check if the first part before the delimiter is a valid grade
            delimiter = '/' if '/' in potential_value else '-'
            parts = potential_value.split(delimiter, 1)
            first_part = parts[0]

            if first_part in VALID_GRADES:
                debug_compound("REFINE", f"First part '{first_part}' is a valid grade, preserving full compound: '{potential_value}'")
                # First part is valid, return the full compound grade
                return potential_value

        # Try normalized match (remove spaces, dashes, etc.)
        cleaned = normalize_text_for_comparison(potential_value)
        for grade in VALID_GRADES:
            grade_cleaned = normalize_text_for_comparison(grade)
            if cleaned == grade_cleaned:
                debug_compound("NORMALIZED MATCH", f"'{potential_value}' matches '{grade}' after normalization")
                return grade

        # Handle special case for grades with suffixes (e.g., WPB-S, WPB-W)
        suffix_match = re.match(r'([A-Z0-9]+)-([A-Z])$', potential_value, re.IGNORECASE)
        if suffix_match:
            base_grade = suffix_match.group(1)
            suffix = suffix_match.group(2)

            # Check if base grade is in our set
            for grade in VALID_GRADES:
                if grade.upper() == base_grade.upper() or \
                   normalize_text_for_comparison(grade) == normalize_text_for_comparison(base_grade):
                    return f"{grade}-{suffix}"

        # Handle compound grades with suffixes (e.g., WP304/304L-W)
        compound_match = re.match(r'([A-Z0-9]+/[A-Z0-9]+)-([A-Z])$', potential_value, re.IGNORECASE)
        if compound_match:
            base_compound = compound_match.group(1)
            suffix = compound_match.group(2)

            # Check if base compound is in our set or close to one
            for grade in VALID_GRADES:
                if grade.upper() == base_compound.upper() or \
                   ('/' in grade and base_compound.upper() in grade.upper()):
                    return f"{grade}-{suffix}"

        # Try partial match for significant overlap
        for grade in VALID_GRADES:
            if (len(grade) >= 3 and len(potential_value) >= 3) and \
               (grade.upper() in potential_value.upper() or potential_value.upper() in grade.upper()):
                # If the potential value ends with -W or -S, preserve it
                if potential_value.endswith('-W') or potential_value.endswith('-S'):
                    suffix = potential_value[-2:]
                    if not grade.endswith(suffix):
                        return f"{grade}{suffix}"
                return grade

        # Try to handle combined values (e.g., TP304L/304L)
        if '/' in potential_value:
            debug_compound("SPLIT COMPOUND", f"Splitting '{potential_value}' into parts")
            parts = re.split(r'/', potential_value)
            recognized_parts = []
            for part in parts:
                if not part:  # Skip empty parts
                    continue
                part_match = find_best_grade_match(part)
                if part_match:
                    recognized_parts.append(part_match)
                    debug_compound("PART MATCH", f"Matched part '{part}' to '{part_match}'")
                elif len(part) > 1:  # Only keep non-trivial parts
                    recognized_parts.append(part)
                    debug_compound("PART PRESERVED", f"Preserving unmatched part '{part}'")

            if recognized_parts:
                # Check if original value had a suffix to preserve
                if potential_value.endswith('-W') or potential_value.endswith('-S'):
                    suffix = potential_value[-2:]
                    result = '/'.join(recognized_parts) + suffix
                    debug_compound("RECOMBINED WITH SUFFIX", f"Recombined parts with suffix: '{result}'")
                    return result

                result = '/'.join(recognized_parts)
                debug_compound("RECOMBINED", f"Recombined parts: '{result}'")
                return result

        # If we've tried everything and found no match, but the value looks like a valid grade format,
        # return it as is rather than returning None, but filter out trash values
        if re.match(r'^[A-Z0-9]+(/[A-Z0-9]+)*(-[A-Z])?$', potential_value, re.IGNORECASE):
            # Filter out single-character grades that aren't in our list (like 'N', 'A', 'B')
            # Also filter out single-digit numbers (like '1', '2', '5')
            if len(potential_value) == 1 and potential_value not in VALID_GRADES:
                debug_compound("FILTERED OUT", f"Single character grade not in valid list: '{potential_value}'")
                return None

            # Filter out standalone 'GR' or 'GR.' which should be prefixes, not grades themselves
            if potential_value in ['GR', 'GR.', 'GRADE']:
                debug_compound("FILTERED OUT", f"Grade prefix mistakenly identified as grade: '{potential_value}'")
                return None

            # Filter out pure numbers unless they're in our valid grades list
            if potential_value.isdigit() and potential_value not in VALID_GRADES:
                debug_compound("FILTERED OUT", f"Numeric value not in valid list: '{potential_value}'")
                return None

            debug_compound("PRESERVED AS IS", f"No match found but format looks valid, preserving: '{potential_value}'")
            return potential_value

        debug_compound("NO MATCH", f"No match found for '{potential_value}'")
        return None

    def extract_astm_value(text_portion):
        """Extract ASTM value from text portion"""
        patterns = [
            # Main ASTM number pattern - matches letter prefix, optional space/hyphen, number with optional decimal
            ASTM_VALUE_PATTERN,

            # Other specific patterns
            r'(?:API|SDR)\s*[-]?\s*\d+[A-Z0-9]*',
            r'ALLOY\s*20',
            r'YOLOY'
        ]

        for pattern in patterns:
            match = re.search(pattern, text_portion, re.IGNORECASE)
            if match:
                potential_value = match.group(0)
                validated_value = find_best_astm_match(potential_value)
                if validated_value:
                    return validated_value
        return None

    entities = []
    # First check for ASTM with grade and suffix pattern (e.g., A234-WPB-S)
    astm_grade_suffix_match = re.search(ASTM_GRADE_SUFFIX_PATTERN, text, re.IGNORECASE)
    if astm_grade_suffix_match:
        # Group 1 is the optional ASTM prefix, which we don't need to store
        prefix = astm_grade_suffix_match.group(1)
        astm_value = astm_grade_suffix_match.group(2)
        grade_value = astm_grade_suffix_match.group(3)
        suffix = astm_grade_suffix_match.group(4) or ''

        print(astm_grade_suffix_match.span(2))
        print(text[astm_grade_suffix_match.span(2)[0]:astm_grade_suffix_match.span(2)[1]])

        entities.append({
            'description': text,
            'type': 'ASTM',
            'subtype': 'astm_value',
            'start': astm_grade_suffix_match.span(1)[0],
            'end': astm_grade_suffix_match.span(1)[1],
        })

        debug_compound("ASTM-GRADE", f"Found pattern: ASTM='{astm_value}', Grade='{grade_value}', Suffix='{suffix}'")

        # Check if grade_value contains a slash (compound grade)
        is_compound_grade = '/' in grade_value
        if is_compound_grade:
            debug_compound("COMPOUND GRADE", f"Detected compound grade in ASTM-Grade pattern: '{grade_value}'")

        # Validate ASTM value
        validated_astm = find_best_astm_match(astm_value)
        if validated_astm:
            found_astm = validated_astm
            debug_compound("ASTM MATCH", f"Matched ASTM '{astm_value}' to '{validated_astm}'")

            # Construct grade with suffix if present
            if suffix:
                grade_with_suffix = f"{grade_value}-{suffix}"
                debug_compound("GRADE WITH SUFFIX", f"Looking for grade with suffix: '{grade_with_suffix}'")
                validated_grade = find_best_grade_match(grade_with_suffix)
            else:
                debug_compound("GRADE WITHOUT SUFFIX", f"Looking for grade: '{grade_value}'")
                validated_grade = find_best_grade_match(grade_value)

            if validated_grade:
                found_grade = validated_grade
                debug_compound("GRADE MATCH", f"Matched grade '{grade_value}' to '{validated_grade}'")
            elif '/' in grade_value:
                # If it's a compound grade that wasn't matched, use it as is
                found_grade = grade_value
                debug_compound("COMPOUND PRESERVED", f"Using compound grade as is: '{grade_value}'")

    # Check for grades with GR. prefix if no grade found yet
    if not found_grade:
        gr_match = re.search(GR_PREFIX_PATTERN, text, re.IGNORECASE)
        if gr_match:
            grade_value = gr_match.group(1)
            debug_compound("GR PREFIX", f"Found grade with GR prefix: '{grade_value}'")

            # Special handling for grades with GR prefix
            # These are more likely to be valid grades even if they're short
            validated_grade = find_best_grade_match(grade_value)
            if validated_grade:
                found_grade = validated_grade
                debug_compound("GR PREFIX MATCH", f"Matched grade with GR prefix: '{grade_value}' to '{validated_grade}'")
            elif len(grade_value) >= 2 or grade_value in VALID_GRADES:  # Accept shorter grades if they follow GR prefix
                found_grade = grade_value
                debug_compound("GR PREFIX PRESERVED", f"Using grade with GR prefix as is: '{grade_value}'")

    # Check for combined ASTM/Grade patterns if no grade found yet
    if not found_grade:
        combined_match = re.search(COMBINED_PATTERN, text, re.IGNORECASE)
        if combined_match:
            combined_value = combined_match.group(0)  # Get the full match including potential suffix
            debug_compound("PATTERN", f"Found compound grade pattern: '{combined_value}'")

            # Check if this is a valid grade
            grade_match = find_best_grade_match(combined_value)
            if grade_match:
                found_grade = grade_match
                debug_compound("MATCH", f"Matched compound value '{combined_value}' to grade '{grade_match}'")

                # Look for ASTM in the first part
                first_part = combined_value.split('/')[0].split('-')[0]  # Remove suffix if present
                debug_compound("FIRST PART", f"Extracting ASTM from first part: '{first_part}'")

                astm_match = re.match(r'([A-Z]+)(\d+)', first_part, re.IGNORECASE)
                if astm_match:
                    letter_prefix = astm_match.group(1).upper()
                    number = astm_match.group(2)
                    potential_astm = f"{letter_prefix}{number}"

                    # Check if this is a valid ASTM code
                    if potential_astm.upper() in VALID_ASTM:
                        found_astm = potential_astm.upper()
                        debug_compound("ASTM MATCH", f"Matched ASTM '{potential_astm}' directly")
                    # Also check if just the letter+number is valid (e.g., A106 from TP106)
                    elif letter_prefix[0] + number in VALID_ASTM:
                        found_astm = letter_prefix[0] + number
                        debug_compound("ASTM MATCH FROM PREFIX", f"Matched ASTM from first letter: '{letter_prefix[0]}{number}'")
            else:
                # If no match found but it's a valid compound format, use it as is
                if '/' in combined_value:
                    found_grade = combined_value
                    debug_compound("PRESERVED", f"No match found, using compound grade as is: '{combined_value}'")

    def replace_astm(match):
        nonlocal found_astm, match_type, review_tag
        full_text = match.group(0)
        after_astm = match.group(1)

        astm_value = extract_astm_value(after_astm)
        if astm_value:
            found_astm = astm_value
            match_type = "prefixed"
            return f"ASTM {after_astm}"
        return full_text

    def replace_standalone(match):
        nonlocal found_astm, match_type, review_tag
        value = match.group(1).strip()

        astm_value = extract_astm_value(value)
        if astm_value:
            found_astm = astm_value
            match_type = "standalone"
            if review:
                review_tag = "astm: Unconfident Match (standalone)"
            return f",ASTM {value},"
        return match.group(0)

    # If we haven't found ASTM from specific patterns, look for explicit ASTM mentions
    if not found_astm:
        astm_pattern = f'ASTM.*?({ASTM_VALUE_PATTERN})'
        text = re.sub(astm_pattern, replace_astm, text, flags=re.IGNORECASE)

    # If still no ASTM found, look for standalone values
    if not found_astm:
        standalone_pattern = (
            r'(?:^|,)\s*'
            f'({ASTM_VALUE_PATTERN})'  # Using same pattern as main ASTM capture
            r'(?:\s*(?:GR\.?|GRADE|CLASS|CL\.?|TYPE)\s*[^,]*)?'  # Optional grade/class info
            r'(?:,|$)'
        )
        text = re.sub(standalone_pattern, replace_standalone, text, flags=re.IGNORECASE)

    # Clean up
    text = re.sub(r',,', ',', text)
    text = re.sub(r'ASTM\s+', 'ASTM ', text)

    # Build metadata tags
    metadata_tags = []
    if found_astm:
        metadata_tags.append(f"astm:{found_astm.lower()}")
        match_type = f"astm:{found_astm}"

    # Final validation of the grade to filter out trash values
    if found_grade:
        # Filter out standalone 'GR' or 'GR.' which should be prefixes, not grades themselves
        if found_grade in ['GR', 'GR.', 'GRADE', 'MI']:
            debug_compound("FINAL FILTER", f"Filtered out grade prefix mistakenly identified as grade: '{found_grade}'")
            found_grade = None
        # Filter out single-character grades that aren't in our list (like 'N', 'A', 'B')
        elif len(found_grade) == 1 and found_grade not in VALID_GRADES:
            debug_compound("FINAL FILTER", f"Filtered out single character grade not in valid list: '{found_grade}'")
            found_grade = None
        # Filter out pure numbers unless they're in our valid grades list
        elif found_grade.isdigit() and found_grade not in VALID_GRADES:
            debug_compound("FINAL FILTER", f"Filtered out numeric value not in valid list: '{found_grade}'")
            found_grade = None

    if found_grade:
        metadata_tags.append(f"grade:{found_grade}")
        if match_type:
            match_type += f", grade:{found_grade}"
        else:
            match_type = f"grade:{found_grade}"

        # Add review tag for compound grades that aren't in our standard list
        if review and ('/' in found_grade or '-' in found_grade) and found_grade not in VALID_GRADES:
            if not review_tag:
                review_tag = f"grade: Compound grade '{found_grade}' not in standard list"
            else:
                review_tag += f", grade: Compound grade '{found_grade}' not in standard list"
            debug_compound("REVIEW TAG", f"Added review tag for compound grade: '{found_grade}'")

    # Add review tag if ASTM is present but no match was found
    if review and not found_astm and re.search(r'ASTM', text, re.IGNORECASE):
        if not review_tag:
            review_tag = "astm: Contains ASTM but no match found"
        else:
            review_tag += ", astm: Contains ASTM but no match found"
        debug_compound("REVIEW TAG", "Added review tag for missing ASTM match")

    metadata_tag = ", ".join(metadata_tags) if metadata_tags else None
    debug_compound("FINAL RESULT", f"Metadata: {metadata_tag}, Review: {review_tag}, Match Type: {match_type}")
    return text, metadata_tag, review_tag, match_type

def normalize_description(description):
    if pd.isna(description):
        return ""

    normalized = str(description) # Ensure string
    normalized = remove_extra_spaces(normalized) # Fix spacing
    normalized = normalize_punctuation(normalized) # Normalize punctuation
    normalized = normalized.upper()
    return normalized

def predict_with_matcher(df: pd.DataFrame):

    df['normalized_description'] = df['material_description'].apply(normalize_description)
    df.fillna('', inplace=True)

    field_options_df = pd.read_excel("data/field_options.xlsx")
    field_options_df.fillna('', inplace=True)

    print(field_options_df.head())

    # Create phrase matcher and covert to NER annotations for training
    nlp = spacy.blank("en")

    # Matcher for ASTM
    astm_matcher = PhraseMatcher(nlp.vocab)

    # Exact matching

    # Define the ASTM items with any additional metadata associated
    astm_items = {
        "ASTM": {},
        "DUPLEX": {"material": "Duplex"},
        "SUPER DUPLEX": {"material": "Duplex"},
        "CS": {"material": "Steel, Carbon", "abbreviated_material": "CS"},
        "SS": {"material": "Steel, Stainless", "abbreviated_material": "SS"},
    }

    # Define additional metadata attached to each ASTM pattern
    for astm_term in field_options_df["astm"].unique().tolist():
        if not astm_term:
            continue
        for astm_prefix, extra_metadata in astm_items.items():
            terms = []
            key = {"astm": astm_term}
            key.update(extra_metadata)

            print("-" in astm_term, astm_term)

            if "-" and "/" in astm_term:
                astm_label = astm_term.split("-")[0]
                grade_label = astm_term.split("-")[1]
                key["astm"] = astm_label
                key["grade"] = grade_label

            elif "-" in astm_term:
                # Note - Might be less accurate.
                astm_label = astm_term.split("-")[0]
                grade_label = astm_term.split("-")[1]
                key["astm"] = astm_label
                key["grade"] = grade_label


            terms.extend([
                f"{astm_prefix} {astm_term}",
                f"{astm_prefix}{astm_term}",
                f"{astm_prefix}. {astm_term}",
            ])
            patterns = [nlp.make_doc(term) for term in terms]
            astm_matcher.add(json.dumps(key), patterns)

    # Matcher for Scheduler
    schedule_matcher = PhraseMatcher(nlp.vocab)
    schedule_prefix = "SCH"
    for schedule_term in field_options_df["schedule"].unique().tolist():
        if not schedule_term:
            continue
        key = {"schedule": schedule_term}
        terms.extend([
            f"{schedule_prefix} {schedule_term}",
            f"{schedule_prefix}{schedule_term}",
            f"{schedule_prefix}. {schedule_term}",
        ])
        patterns = [nlp.make_doc(term) for term in terms]
        schedule_matcher.add(json.dumps(key), patterns)

    results = []

    add_columns = ["rfq_scope", "unit_of_measure", "abbreviated_material", "astm", "grade", "schedule"]
    for col in add_columns:
        if col not in df.columns:
            df[col] = ""

    for row in df.itertuples():
        # index = row.Index
        material_description = row.material_description
        description = row.normalized_description
        print(f"Description: {material_description}")
        print(f"Normalized description: {description}")
        # description = '45 ELBOW,304/304L ASTM A182-F304/304L,SW,CL3000,B16.11' # debug override
        doc = nlp(description)
        tokenized = [token for token in doc]
        astm_matches = astm_matcher(doc)
        schedule_matches = schedule_matcher(doc)

        astm_term = row.astm
        rfq_scope_actual = row.rfq_scope
        unit_of_measure_actual = row.unit_of_measure
        abbreviated_material = row.abbreviated_material
        grade_actual = row.grade
        schedule_actual = row.schedule

        NOT_IMPLEMENTED_VAL = "Not Impl."

        data = {
            # "index": index,
            "material_description": material_description,
            "normalized_description": description,
            "tokenized": tokenized,
            "rfq_scope_act": rfq_scope_actual,
            "rfq_scope_match": NOT_IMPLEMENTED_VAL,
            "unit_of_measure_act": unit_of_measure_actual,
            "unit_of_measure_pred": NOT_IMPLEMENTED_VAL,
            "abbreviated_material_act": abbreviated_material,
            "astm_act": astm_term,
            "grade_act": grade_actual,
            "schedule_act": schedule_actual,
        }

        # Assumes longest regex match is more accurate and contains more data
        longest_match = None
        longest_match_str = None
        extra_data = {}

        # Process ASTM matches
        for match_id, start, end in astm_matches:
            span = doc[start:end]
            match_id_str = doc.vocab.strings[match_id]
            match_data = json.loads(match_id_str)
            print(span.text, match_data)
            if not longest_match or len(span.text) > len(longest_match_str):
                longest_match = match_data
                longest_match_str = span.text
                extra_data["astm_match_span"] = span.text
                extra_data["astm_match_data"] = match_data
                extra_data["astm_start"] = start
                extra_data["astm_end"] = end - 1
                for key, value in match_data.items():
                    extra_data[f"{key}_pred"] = value

        # Process Schedule matches
        longest_match = None
        longest_match_str = None
        for match_id, start, end in schedule_matches:
            span = doc[start:end]
            match_id_str = doc.vocab.strings[match_id]
            match_data = json.loads(match_id_str)
            print(span.text, match_data)
            if not longest_match or len(span.text) > len(longest_match_str):
                longest_match = match_data
                longest_match_str = span.text
                extra_data["schedule_match_span"] = span.text
                extra_data["schedule_match_data"] = match_data
                extra_data["schedule_start"] = start
                extra_data["schedule_end"] = end - 1
                for key, value in match_data.items():
                    extra_data[f"{key}_pred"] = value

        data.update(extra_data)
        results.append(data)
        continue

    new_df = pd.DataFrame(results)
    return new_df


# def extract_training_set(df: pd.DataFrame):

#     df['normalized_description'] = df['material_description'].apply(normalize_description)
#     df.fillna('', inplace=True)

#     astm_df = pd.read_excel("data/astm.xlsx")
#     astm_df.fillna('', inplace=True)

#     print(astm_df.head())

#     # Create phrase matcher and covert to NER annotations for training
#     nlp = spacy.blank("en")
#     matcher = PhraseMatcher(nlp.vocab)

#     for row in astm_df.itertuples():
#         terms = []
#         text = row.text
#         if not text:
#             continue
#         astm_label = row.astm
#         terms.extend([
#             f"CS {text}",
#             f"SS {text}",
#             f"ASTM {text}",
#         ])
#         patterns = [nlp.make_doc(term) for term in terms]
#         matcher.add(astm_label, patterns)

#     data = []

#     training_data = defaultdict(list)
#     for row in df.itertuples():
#         index = row.Index
#         material_description = row.material_description
#         description = row.normalized_description
#         print(f"Description: {material_description}")
#         print(f"Normalized description: {description}")
#         # description = '45 ELBOW,304/304L ASTM A182-F304/304L,SW,CL3000,B16.11' # debug override
#         doc = nlp(description)
#         tokenized = [token for token in doc]
#         matches = matcher(doc)

#         astm_actual = row.astm
#         rfq_scope_actual = row.rfq_scope
#         grade_actual = row.grade
#         if not matches:
#             data.append({
#                 "id": index,
#                 "material_description": material_description,
#                 "normalized_description": description,
#                 "tokenized": tokenized,
#                 "rfq_scope_actual": rfq_scope_actual,
#                 "astm_actual": astm_actual,
#                 "astm_match_id": None,
#                 "astm_match_span": None,
#                 "astm_annotation_valid": False,
#                 "astm_start": None,
#                 "astm_end": None,
#                 "astm_annotation_used": None,
#                 "grade_actual": grade_actual,
#                 "grade_pred": None,
#                 "grade_match_span": None,
#                 "grade_train_valid": False,
#                 "grade_start": None,
#                 "grade_end": None,
#                 "grade_annotation_used": None,
#             })
#             continue

#         for match_id, start, end in matches:
#             span = doc[start:end]
#             print(span.text, doc.vocab.strings[match_id])
#             data.append({
#                 "id": index,
#                 "material_description": material_description,
#                 "normalized_description": description,
#                 "tokenized": tokenized,
#                 "rfq_scope_actual": rfq_scope_actual,
#                 "astm_actual": astm_actual,
#                 "astm_match_id": doc.vocab.strings[match_id],
#                 "astm_match_span": span.text,
#                 "astm_annotation_valid": True,
#                 "astm_start": start,
#                 "astm_end": end,
#                 "astm_decision_used": None,
#                 "grade_actual": grade_actual,
#                 "grade_pred": None,
#                 "grade_match_span": None,
#                 "grade_train_valid": False,
#                 "grade_start": None,
#                 "grade_end": None
#             })

#         continue
#         # Add training data if it has a classification
#         astm_cls = row.astm
#         grade_cls = row.grade
#         if astm_cls:
#             training_data[description].append(astm_cls)

#             normalize_astm(description)

#     new_df = pd.DataFrame(data)
#     training_data = new_df

#     # Format saved data


#     return training_data

#     # Collect metadata tags
#     metadata_tags = []
#     review_tags = []
#     match_types = []

#     # --> Normalize 'Schedule' Values
#     normalized, schedule_tag = normalize_schedule(normalized)
#     if schedule_tag:
#         metadata_tags.append(schedule_tag)

#     # --> Normalize Rating Values
#     normalized, rating_tag, review_rating, match_type_rating = normalize_rating(normalized, review=True)

#     if rating_tag:
#         metadata_tags.append(rating_tag)

#     if review_rating:
#         review_tags.append(review_rating)  # Add to new review_tags list

#     if match_type_rating:
#         match_types.append(match_type_rating)

#     # --> Normalize ASTM Values
#     # First try our enhanced normalize_astm function
#     normalized, astm_metadata, review_astm_new, match_type_astm_new = normalize_astm(normalized, review=True)

#     # If we got results from the enhanced function, use them
#     if astm_metadata:
#         # The astm_metadata might contain both astm and grade tags
#         for tag in astm_metadata.split(", "):
#             metadata_tags.append(tag)

#         # Add review and match types
#         if review_astm_new:
#             review_tags.append(review_astm_new)

#         if match_type_astm_new:
#             for mt in match_type_astm_new.split(", "):
#                 match_types.append(mt)
#     # else:
#     #     # Fall back to the original create_astm_tags function
#     #     astm_tag, match_type_astm, review_astm = create_astm_tags(normalized)
#     #     if astm_tag:
#     #         metadata_tags.append(astm_tag)

#     #     if review_astm:
#     #         review_tags.append(review_astm)

#     #     if match_type_astm:
#     #         match_types.append(match_type_astm)

#     # --> Normalize End Types
#     ends_tag, match_type_ends, review_ends = create_end_tags(normalized)
#     if ends_tag:
#         metadata_tags.append(ends_tag)

#     if review_ends:
#         review_tags.append(review_ends)

#     if match_type_ends:
#         match_types.append(match_type_ends)

#     # Clean up spaces around commas
#     normalized = re.sub(r'\s*,\s*', ',', normalized)


#     ####
#     ####
#     #### Need to figure out the purpose of this block

#     # # Preserve spaces in measurements
#     # parts = normalized.split(',')
#     # for i, part in enumerate(parts):
#     #     if any(char.isdigit() for char in part):
#     #         parts[i] = part.replace(' ', '\x00')  # Temporarily replace spaces with null character
#     # normalized = ','.join(parts)

#     # # Remove spaces from non-measurement parts
#     # normalized = normalized.replace(' ', '')

#     # # Restore spaces in measurements
#     # normalized = normalized.replace('\x00', ' ')

#     return normalized, metadata_tags, review_tags, match_types

def extract_metadata_tags_to_columns(df):
    """
    Extracts metadata tags from the 'metadata_tags' column and creates new columns
    for each tag type prefixed with 'extracted_'.

    Args:
        df (pandas.DataFrame): DataFrame containing a 'metadata_tags' column

    Returns:
        pandas.DataFrame: DataFrame with new columns for each tag type
    """
    # Create a copy of the dataframe to avoid modifying the original
    df = df.copy()

    # Function to parse tags string into a dictionary
    def parse_tags(tags_str):
        if not isinstance(tags_str, str) or not tags_str.strip():
            return {}

        tag_dict = {}
        # Split the string by commas and process each tag
        for tag in tags_str.split(','):
            tag = tag.strip()
            if ':' in tag:
                key, value = tag.split(':', 1)
                tag_dict[key.strip()] = value.strip()
        return tag_dict

    # Apply the parsing function to metadata_tags column
    tags_dicts = df['metadata_tags'].apply(parse_tags)

    # Get all unique tag keys
    all_tag_keys = set()
    for tag_dict in tags_dicts:
        all_tag_keys.update(tag_dict.keys())

    # Create new columns for each tag type
    for tag_key in all_tag_keys:
        col_name = f'extracted_{tag_key}'
        df[col_name] = tags_dicts.apply(lambda x: x.get(tag_key, ''))

    return df

# if __name__ == "__main__":
#     debug_variations = False # Show output for variant generation list
#     run_test_dict = False # Also run test values from 'test_schedule_normalization'
#     sample_output = False

#     # Run our specific test for schedule patterns
#     test_sch_patterns()

#     input_data_path = r"C:\Drawings\Clients\langgraph docs\rfq_template-example.xlsx"
#     output_file = r"C:\Drawings\Clients\langgraph docs\rfq_template-example normalized.xlsx"

#     # Load the Excel file
#     print("Loading data...")

#     df = pd.read_excel(input_data_path)
#     print(f"Loaded {len(df)} rows of data.")

#     # Create columns for both normalized description and metadata
#     print("Normalizing descriptions and generating metadata...")
#     normalized_results = df['material_description'].apply(normalize_description)
#     # normalized_results = df['Material Description'].apply(normalize_description)

#     # Split the results into separate columns
#     df['normalized_description'] = normalized_results.apply(lambda x: x[0])
#     df['metadata_tags'] = normalized_results.apply(lambda x: ','.join(x[1]) if x[1] else '')
#     df['review_tags'] = normalized_results.apply(lambda x: ','.join(x[2]) if x[2] else '')
#     df['match_type'] = normalized_results.apply(lambda x: ','.join(x[3]) if x[3] else '')

#     # After creating metadata_tags column:
#     print("Extracting metadata tags to separate columns...")
#     df = extract_metadata_tags_to_columns(df)

#     # Ensure ASTM column is always uppercase
#     # Define the ASTM formatting function directly
#     def astm_format(val):
#         if val is None:
#             return None
#         if isinstance(val, str):
#             val = val.strip()
#             return val.upper() if val else None
#         return val  # numbers and other types unchanged

#     if 'extracted_astm' in df.columns:
#         print("Formatting Extracted ASTM values to uppercase...")
#         df['extracted_astm'] = df['extracted_astm'].apply(astm_format)

#     if 'astm' in df.columns:
#         print("Formatting ASTM values to uppercase...")
#         df['astm'] = df['astm'].apply(astm_format)

#     # df['normalized_description'] = df['Material Description'].apply(normalize_description) Original Code without metadata tags

#     if sample_output:
#         # Display a sample of the results
#         print("\nSample of original and normalized descriptions:")
#         # sample = df[['Material Description', 'normalized_description', 'metadata_tags']].sample(n=10)
#         sample = df[['material_description', 'normalized_description', 'metadata_tags']].sample(n=10)
#         for _, row in sample.iterrows():
#             # print(f"Original: {row['Material Description']}")
#             print(f"Original: {row['material_description']}")
#             print(f"Normalized: {row['normalized_description']}")
#             print(f"Metadata: {row['metadata_tags']}")
#             print()

#     # Make sure ASTM values are uppercase

#     df['astm_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['astm']) and pd.isna(row['extracted_astm'])) and row['astm'] != row['extracted_astm'] else '',
#         axis=1
#     )
#     df['grade_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['grade']) and pd.isna(row['extracted_grade'])) and row['grade'] != row['extracted_grade'] else '',
#         axis=1
#     )
#     df['ends_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['ends']) and pd.isna(row['extracted_ends'])) and row['ends'] != row['extracted_ends'] else '',
#         axis=1
#     )
#     df['rating_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['rating']) and pd.isna(row['extracted_rating'])) and row['rating'] != row['extracted_rating'] else '',
#         axis=1
#     )
#     df['schedule_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['schedule']) and pd.isna(row['extracted_schedule'])) and row['schedule'] != row['extracted_schedule'] else '',
#         axis=1
#     )
#     df['normalized_description_identical'] = df.apply(
#         lambda row: 'False' if not (pd.isna(row['normalized_description']) and pd.isna(row['material_description'])) and row['normalized_description'] != row['material_description'] else '',
#         axis=1
#     )

#     # Save the updated DataFrame to a new Excel file
#     print(f"Saving normalized data to {output_file}...")
#     df.to_excel(output_file, index=False)
#     print("Done!")


#     if debug_variations:
#         schedule_options = set()
#         for option in ["5", "10", "20", "30", "40", "60", "80", "100", "120", "140", "160", "STD", "XH", "XXH"]:
#             schedule_options.update(generate_variations(f"SCH {option}"))
#             schedule_options.update(generate_variations(f"SCH {option}S"))

#         print(schedule_options)

#     if run_test_dict:
#         test_schedule_normalization() # Test with custom dict


TEST_DATA = [
    "45 LR ELBOW,304/304L SS A403-WP304/304L-S,SMLS,BE,SCH 10S,B16.9",
    "45 LR ELBOW,CS A234-WPB,SMLS,BE,XS,B16.9",
    "BA01SD601LO, BALL VALVE, 316/316L SS A182-F316/F316L, CL150, RF, B16.5, B16.10 LONG, SPLIT BODY, FLOATING, API 608, FULL PORT, 316 SS TRIM, RTFE ST, FG PACKING, FIRE TESTED TO API 607, LO",
    "90 LR ELBOW,DUPLEX SS A815-WPS31803/WPS32205-S,SMLS,BE,SCH 10S,B16.9"
]
USE_TEST_DATA = False

if __name__ == "__main__":

    if USE_TEST_DATA:
        df = pd.DataFrame(TEST_DATA, columns=["material_description"])
        filename = "TEST DATA"
    else:
        filename = r"src\training\data\Training Heartwell1 - EXC_0016 - non fieldmap .xlsx"
        df = pd.read_excel(filename, engine='openpyxl')

    print(df.head())
    predicted_df = predict_with_matcher(df)

    # Save predicted results
    predicted_df["filename"] = filename
    outfile = "debug/RFQ data predictions.xlsx"
    # Save to Excel with freezing the first row
    with pd.ExcelWriter(outfile, engine='openpyxl') as writer:
        predicted_df.to_excel(writer, index=False, sheet_name='Matched Data')
        worksheet = writer.sheets['Matched Data']
        worksheet.freeze_panes = "D2" # Freeze the first 3 columns and first row

        # Set AutoFilter on header row
        max_col = get_column_letter(predicted_df.shape[1])
        worksheet.auto_filter.ref = f"A1:{max_col}1"

        tokenized_explainer = [
            "Related columns. Below explains column suffix name and purpose.",
            "tokenized: Tokenized description for spaCy processing.",
            "{column}_start: Start index of the predicted span within the tokenized description.",
            "{column}_end: End index of the predicted span within the tokenized description. (Note: Exclusive, so end - 1 = last index.)",
            "The start and end index is intended for use with annotating training data.",
            "{column}_match_span: Span of matching text from the tokenized description. Essentially regex/PhraseMatcher",
            "{column}_match_data: Json data of all {key: value} metadata extracted from the ({column}_match_span) text.",
        ]
        tokenized_explainer_str = "\n\n".join(tokenized_explainer)

        actual_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light Blue
        pred_fill   = PatternFill(start_color="FFD580", end_color="FFD580", fill_type="solid")  # Light Orange

        author = "Info"
        for n, column in enumerate(predicted_df.columns):
            letter = get_column_letter(n + 1)
            cell = worksheet[f"{letter}1"]
            comment = None
            height = 160
            width = 400
            fill = None
            if column.endswith("_act"):
                comment = f"Original/Truth value column for `{column[:-4]}` from input data"
                fill = actual_fill
            elif column.endswith("_pred"):
                comment = f"Predicted value column for `{column[:-6]}`\n\n--- Note ---\n\nNot Impl. = Not Implemented - No prediction logic made for column. (TODO)"
                fill = pred_fill
            elif column.endswith(("_match_data", "_start", "_end", "_match_span", "tokenized")):
                comment = tokenized_explainer_str
                height = 400
                width = 500
            elif column == "filename":
                comment = "Source of the original data"

            if comment:
                cell.comment = Comment(comment, author, height=height, width=width)

            if fill:
                worksheet[f"{letter}1"].fill = fill

        # Resize columns to fit header
        for col_num, column_title in enumerate(predicted_df.columns, 1):
            column_letter = get_column_letter(col_num)
            # Adjust width based on header length (add a little extra padding)
            adjusted_width = max(len(str(column_title)) + 5, 10)
            worksheet.column_dimensions[column_letter].width = adjusted_width

        # Left-align all header cells (row 1)
        for cell in worksheet[1]:  # worksheet[1] is the first row
            cell.alignment = Alignment(horizontal='left')

    print(f"Saved predicted results to {outfile}")
