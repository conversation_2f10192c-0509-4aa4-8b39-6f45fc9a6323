"""
Category Intelligence for LangGraph BOM Classification

This module provides component lookup tables, abbreviation dictionaries, and
category-specific classification rules for the LangGraph workflow.
"""

import re
from typing import Dict, Any, Optional


# Component Lookup Tables
COMPONENT_LOOKUP_TABLE = {
    "fittings": {
        "nipples": {
            "aliases": ["Pipe Nipple", "NPL", "Hex Nipple", "Swage Nipple", "Nipple", "NIPPLE"],
            "full_name": "Pipe Nipple",
            "category": "Fittings",
            "fitting_category": "Nipple",
            "description": "Short length of pipe with male threads on both ends",
            "typical_sizes": ["0.5", "0.75", "1", "1.25", "1.5", "2", "3", "4"],
            "common_materials": ["Steel, Carbon", "Steel, Stainless"],
            "typical_astm": ["A106", "A312", "A53"],
            "classification_rules": [
                "If description contains 'Nipple' or 'NPL' → always classify as Fitting, not <PERSON>pe",
                "Nipples are typically 12 inches or shorter in length"
            ]
        },
        "elbows": {
            "aliases": ["90 Elbow", "45 Elbow", "LR Elbow", "SR Elbow", "Elbow", "ELL"],
            "full_name": "Pipe Elbow",
            "category": "Fittings",
            "fitting_category": "90 LR Elbow",  # Default, refined by description
            "description": "Pipe fitting that changes flow direction",
            "variants": {
                "90_lr": "90 LR Elbow",
                "90_sr": "90 SR Elbow",
                "45_lr": "45 Elbow",
                "45_sr": "45 SR Elbow"
            },
            "classification_rules": [
                "90° elbows are most common",
                "LR = Long Radius, SR = Short Radius",
                "Default to LR unless SR explicitly mentioned"
            ]
        },
        "tees": {
            "aliases": ["Tee", "TEE", "Equal Tee", "Reducing Tee"],
            "full_name": "Pipe Tee",
            "category": "Fittings",
            "fitting_category": "Tee",
            "description": "T-shaped pipe fitting with three openings",
            "variants": {
                "equal": "Tee",
                "reducing": "Tee Reducing"
            }
        },
        "reducers": {
            "aliases": ["Reducer", "RED", "Concentric Reducer", "Eccentric Reducer"],
            "full_name": "Pipe Reducer",
            "category": "Fittings",
            "fitting_category": "Reducer Concentric",
            "description": "Fitting that connects pipes of different sizes",
            "variants": {
                "concentric": "Reducer Concentric",
                "eccentric": "Reducer Eccentric"
            }
        },
        "caps": {
            "aliases": ["Cap", "CAP", "End Cap"],
            "full_name": "Pipe Cap",
            "category": "Fittings",
            "fitting_category": "Cap",
            "description": "Fitting that closes the end of a pipe"
        }
    },
    "valves": {
        "ball_valves": {
            "aliases": ["Ball Valve", "BV", "Ball", "Full Port Ball", "Reduced Port Ball"],
            "full_name": "Ball Valve",
            "category": "Valves",
            "valve_type": "Ball Valve",
            "description": "Quarter-turn valve with spherical closure element"
        },
        "gate_valves": {
            "aliases": ["Gate Valve", "GV", "Gate"],
            "full_name": "Gate Valve",
            "category": "Valves",
            "valve_type": "Gate Valve",
            "description": "Linear motion valve with gate-like closure element"
        },
        "check_valves": {
            "aliases": ["Check Valve", "CV", "Check", "Swing Check", "Lift Check"],
            "full_name": "Check Valve",
            "category": "Valves",
            "valve_type": "Check Valve",
            "description": "Valve that allows flow in one direction only"
        }
    },
    "flanges": {
        "weld_neck": {
            "aliases": ["Weld Neck", "WN", "WN Flange"],
            "full_name": "Weld Neck Flange",
            "category": "Flanges",
            "fitting_category": "WN Flange RF",
            "description": "Flange with tapered hub welded to pipe"
        },
        "slip_on": {
            "aliases": ["Slip On", "SO", "SO Flange"],
            "full_name": "Slip On Flange",
            "category": "Flanges",
            "fitting_category": "SO Flange RF",
            "description": "Flange that slips over pipe and is welded"
        },
        "blind": {
            "aliases": ["Blind", "BL", "Blind Flange"],
            "full_name": "Blind Flange",
            "category": "Flanges",
            "fitting_category": "Blind Flange",
            "description": "Solid flange used to close pipe end"
        }
    }
}


# Abbreviation Dictionaries by Category
ABBREVIATION_DICTIONARIES = {
    "flanges": {
        "SO": "Slip On",
        "WN": "Weld Neck",
        "SW": "Socket Weld",
        "THRD": "Threaded",
        "BL": "Blind",
        "LJ": "Lap Joint",
        "RF": "Raised Face",
        "FF": "Flat Face",
        "RTJ": "Ring Type Joint"
    },
    "materials": {
        "CS": "Steel, Carbon",
        "SS": "Steel, Stainless",
        "AS": "Alloy Steel",
        "CI": "Cast Iron",
        "DI": "Iron, Ductile",
        "AL": "Aluminium",
        "CU": "Copper",
        "LTCS": "Low Temp. Carbon Steel"
    },
    "manufacturing": {
        "SMLS": "Seamless",
        "ERW": "Electric Resistance Welded",
        "EFW": "Electric Fusion Welded",
        "SAW": "Submerged Arc Welded",
        "HFW": "High-Frequency Welding"
    },
    "ends": {
        "BE": "Beveled End",
        "PE": "Plain End",
        "SW": "Socket Weld",
        "TE": "Threaded End",
        "FLG": "Flanged"
    },
    "general": {
        "SCH": "Schedule",
        "STD": "Standard",
        "XH": "Extra Heavy",
        "XXH": "Double Extra Heavy",
        "LR": "Long Radius",
        "SR": "Short Radius"
    }
}


# Category-Specific Classification Rules
CATEGORY_INTELLIGENCE = {
    "fitting": {
        "terminology_mapping": {
            "Pipe Nipple": "fitting_category: Nipple",
            "NPL": "fitting_category: Nipple",
            "Pipe Bend": "fitting_category: Pipe Bend",
            "SO": "Socket Weld in flange context",
            "WN": "Weld Neck in flange context",
            "THRD": "Threaded connection"
        },
        "classification_rules": [
            "If description contains 'Nipple' or 'NPL' → always classify as Fitting, not Pipe",
            "If description contains 'Bend' → classify as Fitting (Pipe Bend), not Pipe",
            "Elbow variants: 45°, 90°, LR (Long Radius), SR (Short Radius)",
            "Default elbow type is '90 LR Elbow' unless specified otherwise"
        ],
        "field_priority": [
            "rfq_scope",  # Must be "Fittings"
            "fitting_category",  # Specific fitting type
            "material",
            "astm",
            "grade",
            "size1",
            "size2",
            "schedule",
            "rating",
            "ends",
            "forging"
        ]
    },
    "valve": {
        "terminology_mapping": {
            "BV": "Ball Valve",
            "GV": "Gate Valve",
            "GLV": "Globe Valve",
            "CV": "Check Valve",
            "BFV": "Butterfly Valve"
        },
        "classification_rules": [
            "Ball Valve indicators: 'Ball', 'BV', 'Full Port', 'Reduced Port'",
            "Gate Valve indicators: 'Gate', 'GV', 'Rising Stem', 'Non-Rising'",
            "Check Valve indicators: 'Check', 'CV', 'Swing', 'Lift', 'Wafer'"
        ],
        "field_priority": [
            "rfq_scope",  # Must be "Valves"
            "valve_type",  # Specific valve type
            "material",
            "astm",
            "grade",
            "size1",
            "rating",
            "ends",
            "forging"
        ]
    },
    "pipe": {
        "classification_rules": [
            "Pipe indicators: 'Pipe', 'PIPE', length specifications",
            "Exclude if contains 'Nipple', 'Elbow', 'Tee', 'Reducer'",
            "Schedule is critical for pipes (SCH 40, SCH 80, etc.)"
        ],
        "field_priority": [
            "rfq_scope",  # Must be "Pipe"
            "pipe_category",  # Usually "Pipe"
            "material",
            "astm",
            "grade",
            "size1",
            "schedule",
            "ends",
            "forging"
        ]
    }
}


def get_component_intelligence(category: str) -> Dict[str, Any]:
    """Get category-specific intelligence"""
    return CATEGORY_INTELLIGENCE.get(category.lower(), {})


def get_abbreviation_dictionary(category: str) -> Dict[str, str]:
    """Get abbreviation dictionary for category"""
    return ABBREVIATION_DICTIONARIES.get(category.lower(), {})


def resolve_abbreviations(text: str, category: str = "general") -> str:
    """Resolve abbreviations in text based on category"""
    abbreviations = get_abbreviation_dictionary(category)

    # Also include general abbreviations
    if category != "general":
        abbreviations.update(ABBREVIATION_DICTIONARIES.get("general", {}))

    resolved_text = text
    for abbrev, full_form in abbreviations.items():
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(abbrev) + r'\b'
        resolved_text = re.sub(pattern, full_form, resolved_text, flags=re.IGNORECASE)

    return resolved_text


def lookup_component(description: str) -> Optional[Dict[str, Any]]:
    """Lookup component information based on description"""
    description_upper = description.upper()

    for category, components in COMPONENT_LOOKUP_TABLE.items():
        for component_key, component_info in components.items():
            # Check aliases
            for alias in component_info.get("aliases", []):
                if alias.upper() in description_upper:
                    return {
                        "category": component_info["category"],
                        "component_type": component_key,
                        "full_name": component_info["full_name"],
                        "classification_info": component_info,
                        "match_confidence": 0.9 if alias.upper() == description_upper else 0.7
                    }

    return None


def get_vendor_code_pattern() -> str:
    """Get regex pattern for vendor codes"""
    return r'^VENDOR\s+[A-Z0-9]+$'


def is_vendor_code(description: str) -> bool:
    """Check if description is a vendor code"""
    return bool(re.match(get_vendor_code_pattern(), description.strip().upper()))


if __name__ == "__main__":
    """Test category intelligence functions"""

    print("Testing Category Intelligence Module")
    print("=" * 50)

    # Test component lookup
    test_descriptions = [
        "Pipe Nipple 2\" SCH 40",
        "90 LR Elbow 4\" ASTM A234",
        "Ball Valve 3\" 600#",
        "WN Flange 6\" 300# RF",
        "VENDOR ASYM18456"
    ]

    print("Component Lookup Tests:")
    for desc in test_descriptions:
        result = lookup_component(desc)
        if result:
            print(f"  '{desc}' → {result['category']}: {result['full_name']} (confidence: {result['match_confidence']})")
        else:
            print(f"  '{desc}' → No match found")

    # Test abbreviation resolution
    print("\nAbbreviation Resolution Tests:")
    test_texts = [
        ("WN Flange RF", "flanges"),
        ("SMLS Pipe CS", "materials"),
        ("SCH 40 BE", "general")
    ]

    for text, category in test_texts:
        resolved = resolve_abbreviations(text, category)
        print(f"  '{text}' ({category}) → '{resolved}'")

    # Test vendor code detection
    print("\nVendor Code Detection Tests:")
    vendor_tests = [
        "VENDOR ASYM18456",
        "Pipe SMLS ASTM A106",
        "VENDOR ABC123",
        "Ball Valve 2\""
    ]

    for test in vendor_tests:
        is_vendor = is_vendor_code(test)
        print(f"  '{test}' → Vendor code: {is_vendor}")

    # Test category intelligence
    print("\nCategory Intelligence Tests:")
    categories = ["fitting", "valve", "pipe"]

    for category in categories:
        intelligence = get_component_intelligence(category)
        print(f"  {category.title()} rules: {len(intelligence.get('classification_rules', []))} rules")
        print(f"    Field priority: {intelligence.get('field_priority', [])[:3]}...")

    print("\n" + "=" * 50)
    print("Category intelligence module tested successfully!")
