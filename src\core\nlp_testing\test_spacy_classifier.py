import nlp
from custom_components import create_custom_pipeline
from unit_tests.normalize_description import normalize_description

def test_custom_pipeline():
    """Test the custom SpaCy pipeline on sample descriptions"""
    # Create pipeline with custom components
    nlp = create_custom_pipeline()

    # Sample descriptions
    descriptions = [
        "PIPE NIPPLE 2\" SCH 40 ASTM A106 BE",
        "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9",
        "BALL VALVE 3\" 600# CS",
        "FLANGE 4\" 150# RF ASTM A105",
    ]

    # Process each description
    for desc in descriptions:
        # Normalize description
        normalized_desc = normalize_description(desc)[0]
        print(f"\nOriginal: {desc}")
        print(f"Normalized: {normalized_desc}")

        # Process with SpaCy
        doc = nlp(normalized_desc)

        # Print entities
        print("Entities:")
        for ent in doc.ents:
            print(f"  - {ent.text} ({ent.label_})")

        # Print material type if available
        if doc.has_extension("material_type") and doc._.material_type:
            print(f"Material Type: {doc._.material_type}")

if __name__ == "__main__":
    test_custom_pipeline()