import json
import spacy

from spacy.matcher import PhraseMatcher
from spacy.util import compile_infix_regex
from spacy.tokenizer import Tokenizer

nlp = spacy.blank("en")

# Customize tokenizer: split on commas
infixes = nlp.Defaults.infixes + [r',']  # Add comma to infix rules
infix_re = compile_infix_regex(infixes)
nlp.tokenizer = Tokenizer(nlp.vocab, prefix_search=nlp.tokenizer.prefix_search,
                          suffix_search=nlp.tokenizer.suffix_search,
                          infix_finditer=infix_re.finditer,
                          token_match=nlp.tokenizer.token_match)


# Print tokenization of the text
text = "45 ELBOW,304/304L SS A182-F304/304L,SW,CL3000,B16.11"
doc = nlp(text)
print("Text:", text)
print("Tokens:")
for token in doc:
    print(f"  '{token.text}' (index: {token.i}, start: {token.idx}, end: {token.idx + len(token.text)})")

# Try to match the exact token "CL3000,"
test_pattern = "CL3000,"
test_doc = nlp(test_pattern)
print(f"\nTest pattern: '{test_pattern}'")
print(f"Test pattern tokens: {[token.text for token in test_doc]}")

# Create matcher with various patterns
matcher = PhraseMatcher(nlp.vocab)
patterns = [
    "CL3000",
    "CL3000,",
    "CL 3000",
]

# Add each pattern individually with a unique ID
for i, pattern in enumerate(patterns):
    pattern_doc = nlp.make_doc(pattern)
    key = json.dumps({"rating": "3000", "pattern": pattern})
    matcher.add(key, [pattern_doc])
    print(f"Added pattern: '{pattern}' -> tokens: {[t.text for t in pattern_doc]}")

# Try to match
matches = matcher(doc)
print("\nMatches found:", len(matches))
for match_id, start, end in matches:
    match_key = nlp.vocab.strings[match_id]
    span = doc[start:end]
    print(f"  Match: '{span.text}' at positions {start}-{end}, pattern: {match_key}")
