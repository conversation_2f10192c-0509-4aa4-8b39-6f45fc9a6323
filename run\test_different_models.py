"""
Test different transformer models for better accuracy
"""

import os
import sys
sys.path.append('.')

# Import your existing training function
from run.transformers_train import prepare_training_set, train_model

def test_models():
    """
    Test different transformer models
    """
    models_to_test = [
        "bert-base-uncased",           # Current model
        "roberta-base",                # Often better than BERT
        "distilbert-base-uncased",     # Faster, similar performance
        "microsoft/deberta-base",      # State-of-the-art
        "allenai/scibert_scivocab_uncased",  # Scientific domain
    ]
    
    results = {}
    training_file = "src/training/data/rfq_template-example.xlsx"
    
    for model_name in models_to_test:
        print(f"\n{'='*50}")
        print(f"Testing model: {model_name}")
        print(f"{'='*50}")
        
        try:
            # Prepare training set
            dataset, model, tokenizer, tokenized = prepare_training_set(
                training_file, 
                model_name=model_name
            )
            
            # Train model (reduced epochs for testing)
            metrics = train_model(model, tokenizer, tokenized, epochs=3)
            
            results[model_name] = metrics
            print(f"✅ {model_name}: Success")
            
        except Exception as e:
            print(f"❌ {model_name}: Failed - {str(e)}")
            results[model_name] = {"error": str(e)}
    
    # Print summary
    print(f"\n{'='*50}")
    print("RESULTS SUMMARY")
    print(f"{'='*50}")
    
    for model_name, result in results.items():
        if "error" in result:
            print(f"❌ {model_name}: {result['error']}")
        else:
            print(f"✅ {model_name}: {result}")

if __name__ == "__main__":
    test_models()
