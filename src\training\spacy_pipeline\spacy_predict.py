import json
import spacy
import pandas as pd

from src.core.normalize_description.normalize_description import normalize_description

def predict(model, input_df):
    """Use the model to make predictions on input data"""
    nlp = spacy.load(model)

    # Initialize results storage
    results = {
        'original_description': [],
        'normalized_description': [],
        'predictions': []
    }

    # Process each description
    for _, row in input_df.iterrows():
        original_desc = row['material_description']
        normalized_desc = row['normalized_description']

        # Process with SpaCy
        doc = nlp(normalized_desc)

        # Extract entities
        entities = [(ent.text, ent.label_, ent.start_char, ent.end_char) for ent in doc.ents]

        # Convert entities to a more structured format
        structured_predictions = {}
        for text, label, _, _ in entities:
            structured_predictions[label] = text

        # Store results
        results['original_description'].append(original_desc)
        results['normalized_description'].append(normalized_desc)
        results['predictions'].append(json.dumps(structured_predictions))

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Expand predictions into separate columns
    pred_dfs = results_df['predictions'].apply(json.loads).apply(pd.Series)

    # Add _prediction suffix to all prediction columns
    pred_dfs = pred_dfs.add_suffix('_prediction')

    # Combine with original results
    final_df = pd.concat([results_df.drop('predictions', axis=1), pred_dfs], axis=1)

    # Add original column values if they exist in the input data
    for col in pred_dfs.columns:
        original_col = col.replace('_prediction', '')
        if original_col in input_df.columns:
            # Insert original column before prediction column
            col_idx = final_df.columns.get_loc(col)
            final_df.insert(col_idx, original_col, input_df[original_col])

    return final_df

def run(model_dir, input_file, output_file):
    """Main function to run the prediction process"""
    print(f"Loading model from {model_dir}...")

    # Load input data
    print(f"Loading input data from {input_file}...")
    input_df = pd.read_excel(input_file)
    print(f"Loaded {len(input_df)} rows of input data.")

    # Normalize descriptions
    print("Normalizing descriptions...")
    normalized_results = input_df['material_description'].apply(normalize_description)
    input_df['normalized_description'] = normalized_results.apply(lambda x: x[0])

    # Make predictions
    print("Making predictions...")
    results_df = predict(model_dir, input_df)

    # Save results
    print(f"Saving results to {output_file}...")
    results_df.to_excel(output_file, index=False)

    print("Prediction complete!")

if __name__ == "__main__":
    model_dir = r"debug\spacy_model"
    test_file = r"C:\Drawings\Clients\langgraph docs\training data\rfq_template-example.xlsx"
    output_file = r"c:\Drawings\Clients\langgraph docs\prediction_results.xlsx"

    run(model_dir, test_file, output_file)
