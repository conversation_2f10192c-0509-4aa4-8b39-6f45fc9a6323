import nlp
import pandas as pd
import argparse
import numpy as np
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix
# import matplotlib.pyplot as plt
# import seaborn as sns

from unit_tests.normalize_description import normalize_description

def evaluate_model(model, test_df):
    """Evaluate the model on test data and return metrics"""
    nlp = nlp.load(model)

    # Initialize results storage
    results = {
        'description': [],
        'true_entities': [],
        'pred_entities': [],
        'correct': []
    }

    # Process each description
    for _, row in test_df.iterrows():
        desc = row['normalized_description']
        doc = nlp(desc)

        # Extract predicted entities
        pred_entities = [(ent.text, ent.label_, ent.start_char, ent.end_char) for ent in doc.ents]

        # Get true entities (this would come from your ground truth data)
        # For this example, we're assuming a simplified approach
        true_entities = []  # You would populate this from your annotated data

        # Check correctness (simplified)
        correct = len(pred_entities) == len(true_entities)

        # Store results
        results['description'].append(desc)
        results['true_entities'].append(true_entities)
        results['pred_entities'].append(pred_entities)
        results['correct'].append(correct)

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Calculate overall metrics
    accuracy = results_df['correct'].mean()

    # More detailed metrics would be calculated here based on your specific entity types

    return results_df, accuracy

def generate_confusion_matrix(results_df, output_file):
    """Generate and save confusion matrix visualization"""
    # This is a placeholder - you would need to adapt this to your specific entity types
    # For example, you might want to create a confusion matrix for each entity type

    # Example for a single entity type:
    # y_true = [...]  # True labels
    # y_pred = [...]  # Predicted labels
    # labels = [...]  # Unique label values

    # cm = confusion_matrix(y_true, y_pred, labels=labels)

    # plt.figure(figsize=(10, 8))
    # sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=labels, yticklabels=labels)
    # plt.xlabel('Predicted')
    # plt.ylabel('True')
    # plt.title('Confusion Matrix')
    # plt.savefig(output_file)

    pass

def run(model_dir, test_file, output_file):
    """Main function to run the evaluation process"""
    print(f"Loading model from {model_dir}...")

    # Load test data
    print(f"Loading test data from {test_file}...")
    test_df = pd.read_excel(test_file)
    print(f"Loaded {len(test_df)} rows of test data.")

    # Normalize descriptions
    print("Normalizing descriptions...")
    normalized_results = test_df['material_description'].apply(normalize_description)
    test_df['normalized_description'] = normalized_results.apply(lambda x: x[0])

    # Evaluate model
    print("Evaluating model...")
    results_df, accuracy = evaluate_model(model_dir, test_df)

    # Generate confusion matrix
    print("Generating confusion matrix...")
    generate_confusion_matrix(results_df, f"{output_file}_confusion.png")

    # Save results
    print(f"Saving results to {output_file}...")
    results_df.to_excel(output_file, index=False)

    print(f"Evaluation complete! Overall accuracy: {accuracy:.2f}")

if __name__ == "__main__":
    model_dir = r"c:\Drawings\Clients\langgraph docs\spacy_model"
    test_file = r"c:\Drawings\Clients\langgraph docs\rfq_template-example.xlsx"
    output_file = r"c:\Drawings\Clients\langgraph docs\evaluation_results.xlsx"

    run(model_dir, test_file, output_file)