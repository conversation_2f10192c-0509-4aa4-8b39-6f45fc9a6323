"""
Use fuzzy matching to find candidate spans (approximate string matches).

Use SpanCategorizer to classify those spans.
"""

import spacy
from spacy.tokens import SpanGroup
from spacy.training.example import Example
from rapidfuzz import process

# Sample known brand names
BRANDS = ["Apple", "Beats", "Samsung"]

# Example text with typos
text = "<PERSON><PERSON> is releasing new beets headphones."

# Step 1: Load spaCy pipeline and add components
nlp = spacy.blank("en")
nlp.add_pipe("sentencizer")
span_cat = nlp.add_pipe("span_categorizer")
span_cat.add_label("BRAND")

# Step 2: Use fuzzy matching to find candidate spans
doc = nlp.make_doc(text)

candidates = []
for brand in BRANDS:
    # Search for close matches using rapidfuzz
    matches = process.extract(brand, [text], scorer=process.fuzz.partial_ratio, limit=3)
    for match_text, score, start_idx in matches:
        if score > 80:  # Set threshold for similarity
            end_idx = start_idx + len(match_text)
            span = doc.char_span(start_idx, end_idx, alignment_mode="contract")
            if span:
                candidates.append(span)

# Step 3: Add fuzzy-matched spans to a labeled group (some will be false positives!)
doc.spans["BRAND"] = SpanGroup(doc, spans=candidates, name="BRAND")

# Step 4: Create training Example
# For demo: assume "Appel" and "beets" are actually brands
gold_spans = {
    "BRAND": [
        doc.char_span(0, 5, label="BRAND"),    # "Appel"
        doc.char_span(26, 31, label="BRAND"),  # "beets"
    ]
}
gold_doc = doc.copy()
gold_doc.spans["BRAND"] = SpanGroup(doc, spans=gold_spans["BRAND"], name="BRAND")
example = Example(gold_doc, {"spans": gold_spans})

# Step 5: Train SpanCategorizer on the example
nlp.initialize(lambda: [example])
for _ in range(10):
    nlp.update([example])

# Step 6: Predict on a new text
test_doc = nlp("Appl is releasing new Beats headphones.")
for label, spans in test_doc.spans.items():
    print(f"\nPredicted spans for {label}:")
    for span in spans:
        print(f"  {span.text} ({span.start_char}-{span.end_char}) → {label}")
