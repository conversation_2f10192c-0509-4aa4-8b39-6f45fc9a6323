import spacy
from spacy.training import offsets_to_biluo_tags

nlp = spacy.blank("en")
text = "Apple Inc. is acquiring Beats Electronics."

# Simulated entity spans (char start, char end, label)
entities = [
    (0, 10, "ORG"),   # "Apple Inc."
    # (26, 44, "ORG")   # "Beats Electronics"
    (24, 41, "ORG")   # "Beats Electronics"
]

# Create a Doc
doc = nlp.make_doc(text)

# Get BILUO tags
tags = offsets_to_biluo_tags(doc, entities)

# Display results
for token, tag in zip(doc, tags):
    print(f"{token.text:15} -> {tag}")