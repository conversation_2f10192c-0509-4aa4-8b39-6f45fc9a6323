
def optimize_thresholds(y_true, y_pred_proba, metric='f1'):
    """
    Optimize prediction thresholds for each label individually
    """
    from sklearn.metrics import f1_score, precision_score, recall_score
    import numpy as np

    n_labels = y_true.shape[1]
    optimal_thresholds = []

    for i in range(n_labels):
        best_threshold = 0.5
        best_score = 0

        # Try different thresholds
        for threshold in np.arange(0.1, 0.9, 0.05):
            y_pred_binary = (y_pred_proba[:, i] > threshold).astype(int)

            if metric == 'f1':
                score = f1_score(y_true[:, i], y_pred_binary, zero_division=0)
            elif metric == 'precision':
                score = precision_score(y_true[:, i], y_pred_binary, zero_division=0)
            elif metric == 'recall':
                score = recall_score(y_true[:, i], y_pred_binary, zero_division=0)

            if score > best_score:
                best_score = score
                best_threshold = threshold

        optimal_thresholds.append(best_threshold)

    return optimal_thresholds
