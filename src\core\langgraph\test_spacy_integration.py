"""
Test SpaCy Integration with LangGraph Workflow

Tests the SpaCy-based BOM classification system
"""

import os
import sys
import time
from pathlib import Path


try:
    from spacy_integration import SpacyModelHandler
    from nlp.spacy_classification_nodes import material_analysis_node
    from spacy_langgraph_main import create_classification_workflow
    from state_models import create_initial_state
    print("✅ Successfully imported SpaCy modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the correct directory")
    import traceback
    traceback.print_exc()
    sys.exit(1)

async def test_spacy_integration():
    """Test the SpaCy integration with LangGraph workflow"""

    print("🧪 Testing SpaCy Integration with LangGraph BOM Classification")
    print("=" * 80)

    # Check for SpaCy model directory
    spacy_model_dir = os.environ.get("SPACY_MODEL_DIR", "models/spacy_bom_classifier")
    if not Path(spacy_model_dir).exists():
        print(f"⚠️ SpaCy model directory not found: {spacy_model_dir}")
        print("Will use fallback classification logic")
    else:
        print(f"✅ SpaCy model directory found: {spacy_model_dir}")

    # Test data - real BOM descriptions
    test_cases = [
        {
            "id": "test_001",
            "material_description": "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9",
            "expected_category": "fitting"
        },
        {
            "id": "test_002",
            "material_description": "PIPE NIPPLE 2\" SCH 40 ASTM A106 BE",
            "expected_category": "fitting"
        },
        {
            "id": "test_003",
            "material_description": "BALL VALVE 3\" 600# CS",
            "expected_category": "valve"
        },
        {
            "id": "test_004",
            "material_description": "WN FLANGE 6\" 300# RF ASTM A105",
            "expected_category": "flange"
        },
        {
            "id": "test_005",
            "material_description": "PIPE SMLS 8\" SCH 40 ASTM A106",
            "expected_category": "pipe"
        }
    ]

    # Test each case
    results = []
    total_start_time = time.time()

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}/{len(test_cases)}")
        print(f"   ID: {test_case['id']}")
        print(f"   Description: {test_case['material_description']}")
        print(f"   Expected: {test_case['expected_category']}")

        try:
            # Create initial state
            state = create_initial_state(
                item_id=test_case["id"],
                material_description=test_case["material_description"],
                original_classification={},
                debug_mode=True
            )

            # Add SpaCy model directory to state
            state["spacy_model_dir"] = spacy_model_dir

            # Test Stage 1 - Material Analysis
            start_time = time.time()
            result_state = await material_analysis_node(state)
            processing_time = time.time() - start_time

            # Extract results
            processing_path = result_state.get("processing_path", "unknown")
            extracted_properties = result_state.get("extracted_properties", {})

            # Check if result matches expectation
            is_correct = processing_path == test_case["expected_category"]

            result = {
                "id": test_case["id"],
                "description": test_case["material_description"],
                "expected": test_case["expected_category"],
                "actual": processing_path,
                "correct": is_correct,
                "processing_time": processing_time,
                "extracted_properties": extracted_properties
            }

            results.append(result)

            # Print result
            status_icon = "✅" if is_correct else "❌"
            print(f"   {status_icon} Result: {processing_path}")
            print(f"   ⏱️  Processing time: {processing_time:.2f}s")

            if extracted_properties:
                print(f"   📊 Properties: {list(extracted_properties.keys())}")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            result = {
                "id": test_case["id"],
                "description": test_case["material_description"],
                "expected": test_case["expected_category"],
                "actual": "error",
                "correct": False,
                "processing_time": 0.0,
                "error": str(e)
            }