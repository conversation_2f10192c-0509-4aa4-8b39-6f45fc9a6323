# LangGraph BOM Classification System

A complete end-to-end BOM (Bill of Materials) classification system using LangGraph workflows and Gemini AI models.

## 🚀 Quick Start

### 1. Set Up Your API Key

**Option A: Create a .env file (Recommended)**
```bash
# Create a .env file in the project root directory
GEMINI_API_KEY=your_google_api_key_here
```

**Option B: Set Environment Variable**
```bash
# Windows
set GEMINI_API_KEY=your_google_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_google_api_key_here
```

### 2. Run the Classification

**Easy Interactive Mode (Recommended)**
```bash
cd src/atom/ai_classifier/langgraph_classifier
python run_classification.py
```

This will prompt you for:
- BOM file path (Excel/CSV)
- Output folder
- Optional settings (max items, model choice, debug mode)

**Command Line Mode**
```bash
cd src/atom/ai_classifier/langgraph_classifier
python process_and_export.py --input "path/to/bom.xlsx" --job_folder "path/to/output" --job_name "<PERSON><PERSON><PERSON>"
```

**Test Mode**
```bash
cd src/atom/ai_classifier/langgraph_classifier
python test_real_integration.py
```

## 📊 What You Get

The system creates an Excel file with 4 sheets:

1. **Final_Classifications** - Clean data with exact column structure:
   ```
   id, material_description, rfq_scope, general_category, unit_of_measure,
   material, abbreviated_material, ansme_ansi, astm, grade, rating,
   schedule, coating, forging, ends, item_tag, tie_point, pipe_category,
   valve_type, fitting_category, weld_category, bolt_category, gasket_category
   ```

2. **Detailed_Results** - Processing information and intermediate data
3. **Issues_for_Review** - Items that need manual review
4. **Summary_Statistics** - Processing metrics and performance stats

## 📋 Requirements

### BOM File Format
Your Excel/CSV file must have:
- A `material_description` column with item descriptions
- Optional `id` column (will be auto-generated if missing)

### Example BOM Data
```csv
id,material_description
item_001,"45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9"
item_002,"PIPE NIPPLE 2"" SCH 40 ASTM A106 BE"
item_003,"BALL VALVE 3"" 600# CS"
```

## ⚙️ Options

### Command Line Arguments
```bash
python process_and_export.py [options]

Required:
  --input, -i          Path to BOM Excel/CSV file
  --job_folder, -j     Path to job folder for export

Optional:
  --job_name, -n       Job name for filename
  --model, -m          Model: "gemini-2.0" or "gemini-2.5" (default: gemini-2.0)
  --stage_only, -s     Run Stage 1 only (faster testing)
  --max_items, -x      Maximum number of items to process
  --debug, -d          Enable debug mode
```

### Model Options
- **Gemini 2.0 Flash**: Faster, good for most items
- **Gemini 2.5 Flash**: More accurate, better for complex items

## 🔧 Workflow Stages

The system uses a 4-stage LangGraph workflow:

1. **Material Analysis** - Extracts technical properties and determines processing path
2. **Category-Specific Classification** - Specialized classification based on item type
3. **Q&A Decisions** - Complex reasoning for edge cases
4. **Self-Audit** - Validates and finalizes classifications

## 📁 Example Usage

```bash
# Process a small test batch
python run_classification.py
# Enter: C:\data\my_bom.xlsx
# Enter: C:\output\results
# Enter: 10 (for max items)

# Full command line
python process_and_export.py \
  --input "C:\data\my_bom.xlsx" \
  --job_folder "C:\output\results" \
  --job_name "Project_ABC" \
  --model "gemini-2.5" \
  --debug
```

## 🎯 Success Metrics

The system provides:
- **Processing time** per item
- **Success rate** (items processed without errors)
- **Accuracy metrics** (items without issues)
- **Model usage statistics** (API calls, tokens used)

## 🚨 Troubleshooting

### "API Key not found"
- Ensure your .env file is in the project root
- Check that your API key is valid
- Try setting the environment variable directly

### "File not found"
- Use full file paths
- Ensure the file exists and is accessible
- Check file permissions

### "Missing required columns"
- Ensure your file has a `material_description` column
- Check column names (case-sensitive)
- Verify the file format (Excel/CSV)

### "Import errors"
- Run from the correct directory: `src/atom/ai_classifier/langgraph_classifier`
- Ensure all dependencies are installed
- Check Python path configuration

## 📈 Performance Tips

- Use **Gemini 2.0 Flash** for faster processing
- Enable **--stage_only** for quick testing
- Use **--max_items** to limit processing for tests
- Process in batches for very large datasets (1000+ items)

## 🔄 Recent Improvements

✅ **Fixed JSON parsing issues** - Self-audit stage now correctly processes responses
✅ **Added exact column structure** - Final export matches your specification exactly
✅ **Improved API key handling** - Automatic .env file loading
✅ **Enhanced error messages** - Better troubleshooting information
✅ **Added interactive runner** - Easy-to-use interface without command line complexity
