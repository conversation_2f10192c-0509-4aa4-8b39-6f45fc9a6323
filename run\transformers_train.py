import os
import torch
import pandas as pd

from datasets import Dataset
from transformers import <PERSON>Token<PERSON>, AutoModelForSequenceClassification

from sklearn.preprocessing import MultiLabelBinarizer
from transformers import TrainingArguments, Trainer, DataCollatorWithPadding
from sklearn.metrics import accuracy_score, classification_report
import numpy as np
from transformers import AutoModelForSequenceClassification
import torch.nn as nn
from transformers.modeling_outputs import SequenceClassifierOutput

USE_CUDA = True
DEVICE = "cuda" if USE_CUDA and torch.cuda.is_available() else "cpu"


# Labels that actually exist in the dataset
labels = [
    "rfq_scope",
    "general_category",
    "unit_of_measure",
    "material",
    "abbreviated_material",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    "forging",
    "ends",
    "pipe_category",
    "valve_type",
    "fitting_category",
    "weld_category",
    "bolt_category",
    "gasket_category"
]

class MultiLabelClassifier(nn.Module):
    def __init__(self, base_model_name, num_labels, class_weights=None):
        super().__init__()
        self.base = AutoModelForSequenceClassification.from_pretrained(
            base_model_name,
            num_labels=num_labels,
            problem_type="multi_label_classification"
        )
        self.class_weights = class_weights
        if class_weights is not None:
            self.class_weights = torch.tensor(class_weights, dtype=torch.float32)

    def forward(self, input_ids=None, attention_mask=None, token_type_ids=None,
                position_ids=None, head_mask=None, inputs_embeds=None, labels=None,
                output_attentions=None, output_hidden_states=None, return_dict=None, **kwargs):
        # Filter out any unexpected arguments
        valid_args = {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            'position_ids': position_ids,
            'head_mask': head_mask,
            'inputs_embeds': inputs_embeds,
            'output_attentions': output_attentions,
            'output_hidden_states': output_hidden_states,
            'return_dict': return_dict
        }
        # Remove None values
        valid_args = {k: v for k, v in valid_args.items() if v is not None}

        # Pass all arguments including labels to the base model
        return self.base(**valid_args)


def prepare_training_set(file: str, test_size: float = 0.2, model_name = "bert-base-uncased"):
    df = pd.read_excel(file)

    # Debug: Check what's in the label columns
    print("Label columns analysis:")
    for label in labels:
        if label in df.columns:
            unique_vals = df[label].value_counts().head()
            print(f"{label}: {unique_vals.to_dict()}")
        else:
            print(f"{label}: Column not found")
    print("---")

    # Combine all label columns into a multi-label target
    # Fill NaN values with empty string, but only keep non-empty values
    df_labels = df[labels].fillna('')

    # Create list of non-empty labels for each row
    label_lists = []
    for idx, row in df_labels.iterrows():
        row_labels = [str(val) for val in row.values if str(val).strip() != '' and str(val).strip().lower() != 'nan']
        if not row_labels:  # If no valid labels, add a placeholder
            row_labels = ['unknown']
        label_lists.append(row_labels)

    df["labels"] = label_lists

    # Binarize labels
    global mlb  # Make mlb accessible globally
    mlb = MultiLabelBinarizer()
    label_vectors = mlb.fit_transform(df["labels"])
    df["label_vector"] = label_vectors.tolist()

    print(f"Number of unique labels: {len(mlb.classes_)}")
    print(f"Label classes: {mlb.classes_[:10]}...")  # Show first 10 classes
    print(f"Label vector shape: {label_vectors.shape}")
    print(f"Sample label vector: {label_vectors[0]}")

    # Check label distribution and calculate class weights
    label_sums = label_vectors.sum(axis=0)
    print(f"Label frequency (top 10): {sorted(zip(mlb.classes_, label_sums), key=lambda x: x[1], reverse=True)[:10]}")
    print(f"Average labels per sample: {label_vectors.sum(axis=1).mean():.2f}")

    # Calculate class weights for imbalanced labels
    total_samples = len(label_vectors)
    pos_weights = []
    for i in range(len(mlb.classes_)):
        pos_count = label_sums[i]
        neg_count = total_samples - pos_count
        if pos_count > 0:
            weight = neg_count / pos_count
        else:
            weight = 1.0
        pos_weights.append(weight)

    print(f"Class weights range: {min(pos_weights):.2f} to {max(pos_weights):.2f}")
    global class_weights
    class_weights = pos_weights

    # Hugging Face dataset
    dataset = Dataset.from_pandas(df[['material_description', 'label_vector']])
    dataset = dataset.train_test_split(test_size=test_size)

    # Load tokenizer and model from Hugging Face
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    # Use standard model for now (class weights causing issues)
    model = MultiLabelClassifier("bert-base-uncased", num_labels=len(mlb.classes_), class_weights=None)
    model.to(DEVICE)

    def preprocess(examples):
        # Ensure material_description is a list of strings
        descriptions = examples["material_description"]

        # Handle both single examples and batches
        if isinstance(descriptions, list):
            # Make sure each item is a string
            descriptions = [str(item) if item is not None else "" for item in descriptions]
        else:
            # If it's not a list, convert it to a string and wrap in a list
            descriptions = [str(descriptions) if descriptions is not None else ""]

        # Tokenize with consistent parameters - don't use padding=True in batched mode
        tokenized = tokenizer(
            descriptions,
            truncation=True,
            padding=False,  # Let the trainer handle padding
            max_length=128,  # Increased max length for better context
            return_tensors=None  # Don't return tensors, let trainer handle it
        )

        # Handle labels properly for batched processing
        label_vectors = examples["label_vector"]
        if isinstance(label_vectors, list) and len(label_vectors) > 0:
            # Check if we have a batch of label vectors
            if isinstance(label_vectors[0], list):
                # Convert to float for multi-label classification
                tokenized["labels"] = [[float(x) for x in label_vec] for label_vec in label_vectors]
            else:
                # Single label vector, wrap in list and convert to float
                tokenized["labels"] = [[float(x) for x in label_vectors]]
        else:
            # Fallback for edge cases
            if isinstance(label_vectors, list):
                tokenized["labels"] = [[float(x) for x in label_vectors]]
            else:
                tokenized["labels"] = [[float(label_vectors)]]

        return tokenized

    tokenized = dataset.map(preprocess, batched=True)

    # Remove the original columns that are not needed for training
    tokenized = tokenized.remove_columns(['material_description', 'label_vector'])

    return dataset, model, tokenizer, tokenized


def train_model(model, tokenizer, tokenized):
    """
    Train a sequence classification model on the dataset

    Args:
        model: A pre-trained model instance
        tokenizer: A tokenizer instance
        tokenized: A tokenized dataset instance

    Returns:
        A trained model instance
    """
    def compute_metrics(eval_pred):
        logits, labels = eval_pred
        # For multi-label classification, use sigmoid and threshold
        predictions = (torch.sigmoid(torch.tensor(logits)) > 0.5).int().numpy()

        # Calculate accuracy for multi-label (exact match)
        exact_match = np.all(predictions == labels, axis=1).mean()

        # Calculate per-label accuracy
        per_label_acc = np.mean(predictions == labels)

        return {
            "exact_match_accuracy": exact_match,
            "per_label_accuracy": per_label_acc
        }

    args = TrainingArguments(
        output_dir="./multilabel-clf",
        per_device_train_batch_size=8,   # Conservative batch size
        per_device_eval_batch_size=8,
        num_train_epochs=7,   # More epochs for better learning
        eval_strategy="epoch",
        save_strategy="epoch",
        logging_steps=10,
        learning_rate=3e-5,   # Slightly higher learning rate
        weight_decay=0.01,
        warmup_steps=100,     # Warmup steps
        remove_unused_columns=False,
        load_best_model_at_end=True,
        metric_for_best_model="per_label_accuracy",
        greater_is_better=True,
        gradient_accumulation_steps=2,  # Effective batch size = 16
        seed=42,  # Reproducible results
    )

    # Create data collator for proper padding
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

    trainer = Trainer(
        model=model,
        args=args,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics
    )

    trainer.train()

def run_inference(tokenizer, model, text: str):
    """
    Make Predictions - returns predicted labels as tuple
    """
    inputs = tokenizer(text, return_tensors="pt")
    # Move to the same device as the model
    inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

    # Set model to evaluation mode. Deterministic predictions across runs
    model.eval()

    # Inference with optimized threshold
    with torch.no_grad():
        logits = model(**inputs).logits
        sigmoid_probs = torch.sigmoid(logits)
        preds = (sigmoid_probs > 0.3).int().cpu().numpy()[0]  # More conservative threshold
        # Convert to numpy array for inverse_transform
        preds_array = np.array([preds])
        predicted_labels = mlb.inverse_transform(preds_array)[0]
        return predicted_labels

def run_batch_inference(tokenizer, model, texts):
    """
    Run inference on a batch of texts for efficiency
    """
    model.eval()
    all_predictions = []

    # Process in batches for efficiency
    batch_size = 16
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]

        # Tokenize batch
        inputs = tokenizer(
            batch_texts,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=128
        )
        inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

        # Get predictions with optimized threshold
        with torch.no_grad():
            logits = model(**inputs).logits
            # Use different thresholds for different types of predictions
            sigmoid_probs = torch.sigmoid(logits)
            preds = (sigmoid_probs > 0.3).int().cpu().numpy()  # More conservative threshold

            # Convert predictions to labels
            batch_predictions = mlb.inverse_transform(preds)
            all_predictions.extend(batch_predictions)

    return all_predictions

def set_seed(seed=42):
    """
    Set a seed (e.g. 42) to ensure reproducibility
    Without it, the same script can give slightly different model weights and performance each run
    """
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

set_seed(42)


def run(filename: str, test_file: str=None, output_file: str="debug/predictions.xlsx"):
    """
    Example training process and inference test
    """
    dataset, model, tokenizer, tokenized = prepare_training_set(filename)
    train_model(model, tokenizer, tokenized)

    if not test_file:
        return

    # Load test data
    test_df = pd.read_excel(test_file)
    print(f"Running inference on {len(test_df)} samples...")

    # Get predictions for all material descriptions
    descriptions = test_df["material_description"].tolist()
    predictions = run_batch_inference(tokenizer, model, descriptions)

    # Create output dataframe
    output_df = test_df.copy()

    # Add prediction columns for each label
    for label in labels:
        # Add actual column (rename existing if it exists)
        if label in output_df.columns:
            output_df[f"{label}_actual"] = output_df[label]
        else:
            output_df[f"{label}_actual"] = ""

        # Add prediction column
        output_df[f"{label}_pred"] = ""

    # Create a mapping from predicted values to their corresponding label columns
    # We need to check which column each predicted value belongs to
    value_to_column_map = {}
    for label_col in labels:
        if label_col in test_df.columns:
            unique_values = test_df[label_col].dropna().astype(str).unique()
            for value in unique_values:
                if value and value.strip() != '' and value.lower() != 'nan':
                    value_to_column_map[value] = label_col

    print(f"Created mapping for {len(value_to_column_map)} unique values to columns")
    print(f"Sample mappings: {list(value_to_column_map.items())[:10]}")
    print(f"Sample predictions: {predictions[:2]}")

    # Fill in predictions
    predictions_filled = 0
    for idx, pred_labels in enumerate(predictions):
        if idx < 3:  # Debug first few rows
            print(f"Row {idx} predictions: {pred_labels}")

        # Handle case where pred_labels might be a tuple or list
        if isinstance(pred_labels, (tuple, list)):
            for pred_label in pred_labels:
                if pred_label and pred_label.strip() != '':
                    # Find which column this predicted value belongs to
                    if pred_label in value_to_column_map:
                        column_name = value_to_column_map[pred_label]
                        output_df.loc[idx, f"{column_name}_pred"] = pred_label
                        predictions_filled += 1
                        if idx < 3:  # Debug first few rows
                            print(f"  Mapped '{pred_label}' to column '{column_name}'")
                    elif idx < 3:
                        print(f"  '{pred_label}' not found in mapping")
        else:
            # Single prediction
            if pred_labels and pred_labels.strip() != '':
                if pred_labels in value_to_column_map:
                    column_name = value_to_column_map[pred_labels]
                    output_df.loc[idx, f"{column_name}_pred"] = pred_labels
                    predictions_filled += 1

    print(f"Total predictions filled: {predictions_filled}")

    # Reorder columns to put actual and pred side by side
    new_columns = ["material_description"]

    # Add other original columns first (except the label columns we're reorganizing)
    for col in output_df.columns:
        if col not in labels and col != "material_description" and not col.endswith("_actual") and not col.endswith("_pred"):
            new_columns.append(col)

    # Add label columns in pairs (actual, pred)
    for label in labels:
        if f"{label}_actual" in output_df.columns:
            new_columns.append(f"{label}_actual")
        if f"{label}_pred" in output_df.columns:
            new_columns.append(f"{label}_pred")

    # Reorder the dataframe
    output_df = output_df[new_columns]

    # Calculate accuracy statistics
    accuracy_stats = []

    for label in labels:
        actual_col = f"{label}_actual"
        pred_col = f"{label}_pred"

        if actual_col in output_df.columns and pred_col in output_df.columns:
            # Get non-empty actual values
            actual_values = output_df[actual_col].fillna('').astype(str)
            pred_values = output_df[pred_col].fillna('').astype(str)

            # Calculate metrics
            total_samples = len(actual_values)

            # Exact matches (both actual and predicted are non-empty and equal)
            exact_matches = ((actual_values != '') & (pred_values != '') &
                           (actual_values == pred_values)).sum()

            # Samples with actual values (ground truth available)
            has_actual = (actual_values != '').sum()

            # Samples with predictions
            has_predictions = (pred_values != '').sum()

            # True positives, false positives, false negatives for each unique value
            unique_actual = set(actual_values[actual_values != ''])
            unique_pred = set(pred_values[pred_values != ''])
            all_values = unique_actual.union(unique_pred)

            precision_scores = []
            recall_scores = []
            f1_scores = []

            for value in all_values:
                if value == '':
                    continue

                tp = ((actual_values == value) & (pred_values == value)).sum()
                fp = ((actual_values != value) & (pred_values == value)).sum()
                fn = ((actual_values == value) & (pred_values != value)).sum()

                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

                precision_scores.append(precision)
                recall_scores.append(recall)
                f1_scores.append(f1)

            # Calculate averages
            avg_precision = sum(precision_scores) / len(precision_scores) if precision_scores else 0
            avg_recall = sum(recall_scores) / len(recall_scores) if recall_scores else 0
            avg_f1 = sum(f1_scores) / len(f1_scores) if f1_scores else 0

            # Calculate accuracy rates
            exact_match_rate = exact_matches / has_actual if has_actual > 0 else 0
            prediction_coverage = has_predictions / total_samples

            accuracy_stats.append({
                'Label': label,
                'Total_Samples': total_samples,
                'Samples_with_Actual': has_actual,
                'Samples_with_Predictions': has_predictions,
                'Exact_Matches': exact_matches,
                'Exact_Match_Rate': round(exact_match_rate, 4),
                'Prediction_Coverage': round(prediction_coverage, 4),
                'Avg_Precision': round(avg_precision, 4),
                'Avg_Recall': round(avg_recall, 4),
                'Avg_F1_Score': round(avg_f1, 4),
                'Unique_Actual_Values': len(unique_actual),
                'Unique_Predicted_Values': len(unique_pred)
            })

    # Create accuracy summary DataFrame
    accuracy_df = pd.DataFrame(accuracy_stats)

    # Save to Excel with multiple sheets
    if output_file is None:
        output_file = test_file.replace('.xlsx', '_predictions.xlsx')
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Main predictions sheet
        output_df.to_excel(writer, sheet_name='Predictions', index=False)

        # Accuracy summary sheet
        accuracy_df.to_excel(writer, sheet_name='Accuracy_Summary', index=False)

        # Overall summary statistics
        overall_stats = {
            'Metric': [
                'Total Samples',
                'Average Exact Match Rate',
                'Average Prediction Coverage',
                'Average Precision',
                'Average Recall',
                'Average F1 Score',
                'Labels with >50% Accuracy',
                'Labels with >80% Accuracy',
                'Labels with 100% Coverage'
            ],
            'Value': [
                len(output_df),
                round(accuracy_df['Exact_Match_Rate'].mean(), 4),
                round(accuracy_df['Prediction_Coverage'].mean(), 4),
                round(accuracy_df['Avg_Precision'].mean(), 4),
                round(accuracy_df['Avg_Recall'].mean(), 4),
                round(accuracy_df['Avg_F1_Score'].mean(), 4),
                (accuracy_df['Exact_Match_Rate'] > 0.5).sum(),
                (accuracy_df['Exact_Match_Rate'] > 0.8).sum(),
                (accuracy_df['Prediction_Coverage'] == 1.0).sum()
            ]
        }
        overall_df = pd.DataFrame(overall_stats)
        overall_df.to_excel(writer, sheet_name='Overall_Summary', index=False)

    print(f"Predictions saved to: {output_file}")
    print(f"Excel file contains 3 sheets: Predictions, Accuracy_Summary, Overall_Summary")

    # Print some sample predictions for verification
    print("\nSample predictions:")
    for i in range(min(5, len(test_df))):
        text = descriptions[i]
        pred_labels = predictions[i]
        print(f"Text: {text[:100]}...")
        print(f"Predicted Labels: {pred_labels}")
        print("---")

if __name__ == "__main__":
    training_file = r"src\training\data\rfq_template-example.xlsx"
    test_file = r"src\training\data\rfq_template-example.xlsx"
    output_file = r"debug\rfq_template-example_predictions.xlsx"
    run(training_file, test_file, output_file)